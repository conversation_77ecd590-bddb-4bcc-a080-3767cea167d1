# 虚拟光学实验室 - 交互操作指南

## 🎮 用户交互功能

### 🖱️ 鼠标操作

#### 基础视角控制
- **左键拖拽** - 旋转3D视角
- **滚轮** - 缩放场景
- **右键拖拽** - 平移视图

#### 对象交互
- **右键点击空白处** - 显示添加菜单
- **左键点击对象** - 选择对象
- **拖拽对象** - 移动透镜或光源位置
- **双击对象** - 查看对象属性
- **右键点击对象** - 对象操作菜单

### ⌨️ 键盘快捷键

- **R键** - 重置场景到默认状态
- **H键** - 显示/隐藏GUI控制面板
- **Esc键** - 取消当前选择
- **Delete/Backspace键** - 删除选中的对象

### 📋 右键菜单功能

当您在空白处右键点击时，会出现上下文菜单：

1. **添加凸透镜** - 在点击位置创建凸透镜
2. **添加凹透镜** - 在点击位置创建凹透镜
3. **添加点光源** - 在点击位置创建点光源
4. **添加平行光** - 在点击位置创建平行光源

## 🔧 GUI控制面板

### 透镜控制
- **透镜类型** - 选择凸透镜或凹透镜
- **焦距** - 调节透镜的焦距 (1-20)
- **直径** - 调节透镜的直径 (1-10)
- **厚度** - 调节透镜的厚度 (0.1-2)
- **位置** - 精确设置透镜的X、Y、Z坐标

### 光源控制
- **光源类型** - 选择点光源或平行光
- **强度** - 调节光源亮度 (0-5)
- **颜色** - 设置光的颜色
- **射线数量** - 控制显示的光线条数 (4-36)
- **位置** - 精确设置光源的X、Y、Z坐标
- **方向** - 设置平行光的方向向量

### 演示场景
预设的实验场景，一键加载：
- **凸透镜会聚** - 平行光通过凸透镜会聚
- **凹透镜发散** - 平行光通过凹透镜发散
- **点光源成像** - 点光源通过透镜的光路
- **双透镜系统** - 复合透镜光学系统
- **彩色光源** - 不同颜色光源演示
- **焦距对比** - 不同焦距透镜对比
- **复杂系统** - 凹凸透镜组合

### 渲染控制
- **显示光线** - 开启/关闭光线显示
- **最大光线长度** - 控制光线的显示长度
- **最大反射次数** - 控制光线的反射次数

### 对象管理
- **透镜数量** - 显示当前场景中的透镜数量
- **光源数量** - 显示当前场景中的光源数量
- **选择模式** - 开启/关闭选择模式

## 🎯 实际操作流程

### 创建基础实验

1. **清空场景**
   - 点击"操作" → "清空场景"

2. **添加透镜**
   - 方法1：在空白处右键 → 选择"添加凸透镜"
   - 方法2：使用GUI面板设置参数 → 点击"添加透镜"

3. **添加光源**
   - 方法1：在空白处右键 → 选择"添加点光源"
   - 方法2：使用GUI面板设置参数 → 点击"添加光源"

4. **调整位置**
   - 直接拖拽对象到合适位置
   - 或在GUI面板中精确设置坐标

5. **观察光路**
   - 光线会自动计算并显示
   - 可以调节光线显示参数

### 编辑现有对象

1. **选择对象**
   - 左键点击要编辑的透镜或光源
   - 对象会高亮显示

2. **移动对象**
   - 拖拽选中的对象到新位置
   - 光线会实时更新

3. **查看属性**
   - 双击对象查看详细属性
   - 在GUI面板中调节参数

4. **删除对象**
   - 选中对象后按Delete键
   - 或使用右键菜单删除

### 高级操作

1. **复杂光学系统**
   - 添加多个透镜和光源
   - 观察光线的多次折射

2. **参数实验**
   - 调节透镜焦距观察焦点变化
   - 改变光源位置观察成像效果

3. **保存场景**
   - 使用演示场景功能
   - 或记录当前参数设置

## 💡 使用技巧

### 精确操作
- 使用GUI面板进行精确的数值设置
- 结合拖拽和数值输入获得最佳效果

### 视角控制
- 经常使用R键重置视角
- 利用不同角度观察光路

### 性能优化
- 适当控制射线数量
- 避免同时添加过多对象

### 实验设计
- 从简单场景开始
- 逐步增加复杂度
- 利用演示场景学习

## 🔍 故障排除

### 对象无法选择
- 确保点击的是透镜或光源本体
- 检查是否开启了选择模式

### 拖拽不响应
- 确保左键按下并拖拽
- 检查对象是否已选中

### 右键菜单不显示
- 确保在空白处右键点击
- 检查浏览器是否阻止了右键菜单

### 光线不显示
- 检查"显示光线"选项是否开启
- 确保场景中有光源和透镜

## 🎓 学习建议

1. **从演示场景开始** - 了解基本光学现象
2. **尝试手动创建** - 练习交互操作
3. **实验参数变化** - 理解参数对光路的影响
4. **创建复杂系统** - 探索高级光学原理

通过这些交互功能，您可以直观地探索光学原理，进行各种光学实验，并深入理解透镜和光源的工作原理！
