# 虚拟光学实验室

一个基于Three.js的交互式虚拟光学实验室，支持透镜和光源的物理仿真。

## 功能特性

### 🔬 光学组件
- **透镜系统**
  - 凸透镜（会聚透镜）
  - 凹透镜（发散透镜）
  - 可调节焦距（1-20单位）
  - 可调节直径和厚度
  - 实时位置调整

- **光源系统**
  - 点光源（360度发射光线）
  - 平行光源（定向光束）
  - 可调节光强度
  - 可调节光颜色
  - 可调节射线数量

### ⚡ 物理仿真
- **光线追踪**
  - 实时光线计算
  - 折射定律（斯涅尔定律）
  - 菲涅尔反射
  - 全反射现象
  - 多次反射支持

- **光学计算**
  - 透镜焦点计算
  - 光路分析
  - 强度衰减
  - 色散效果

### 🎮 交互控制
- **3D场景操作**
  - 鼠标拖拽旋转视角
  - 滚轮缩放
  - 平移视图

- **实时参数调节**
  - GUI控制面板
  - 实时参数修改
  - 场景重置功能

## 快速开始

### 安装依赖
```bash
npm install
```

### 启动开发服务器
```bash
npm run dev
```

### 构建生产版本
```bash
npm run build
```

## 使用说明

### 基本操作
1. **视角控制**
   - 鼠标左键拖拽：旋转视角
   - 鼠标滚轮：缩放场景
   - 鼠标右键拖拽：平移视图

2. **键盘快捷键**
   - `R` 键：重置场景
   - `H` 键：显示/隐藏控制面板

### 添加透镜
1. 在右侧控制面板选择"透镜控制"
2. 设置透镜类型（凸透镜/凹透镜）
3. 调节焦距、直径、厚度参数
4. 设置透镜位置坐标
5. 点击"添加透镜"按钮

### 添加光源
1. 在控制面板选择"光源控制"
2. 选择光源类型（点光源/平行光）
3. 调节光强度和颜色
4. 设置光源位置
5. 对于平行光，还需设置光线方向
6. 点击"添加光源"按钮

### 观察光学现象
- **会聚现象**：使用凸透镜观察光线会聚到焦点
- **发散现象**：使用凹透镜观察光线发散
- **折射现象**：观察光线通过透镜时的折射
- **焦距效应**：调节焦距观察焦点位置变化

## 技术架构

### 核心技术栈
- **Three.js** - 3D图形渲染
- **TypeScript** - 类型安全的JavaScript
- **Vite** - 快速构建工具
- **lil-gui** - 参数控制界面

### 项目结构
```
src/
├── core/           # 核心系统
│   ├── VirtualLab.ts      # 主实验室类
│   └── SceneManager.ts    # 场景管理
├── optics/         # 光学组件
│   ├── Lens.ts            # 透镜类
│   ├── LensManager.ts     # 透镜管理器
│   ├── LightSource.ts     # 光源类
│   └── LightSourceManager.ts # 光源管理器
├── physics/        # 物理计算
│   └── RayTracer.ts       # 光线追踪器
├── ui/            # 用户界面
│   └── UIController.ts    # UI控制器
└── utils/         # 工具函数
    └── MathUtils.ts       # 数学工具
```

### 光学原理

#### 透镜成像
- 凸透镜：平行光线会聚到焦点
- 凹透镜：平行光线发散，虚焦点在透镜前方

#### 折射定律
```
n₁ sin θ₁ = n₂ sin θ₂
```
其中：
- n₁, n₂ 为两种介质的折射率
- θ₁, θ₂ 为入射角和折射角

#### 薄透镜公式
```
1/f = 1/u + 1/v
```
其中：
- f 为焦距
- u 为物距
- v 为像距

## 扩展功能

### 计划中的功能
- [ ] 复合透镜系统
- [ ] 光谱分析
- [ ] 波长相关的色散
- [ ] 光学仪器模拟（显微镜、望远镜）
- [ ] 实验数据导出
- [ ] 预设实验场景

### 自定义开发
项目采用模块化设计，可以轻松扩展：

1. **添加新的光学元件**
   - 继承基础光学组件类
   - 实现特定的光学计算

2. **扩展光线追踪算法**
   - 修改 `RayTracer.ts`
   - 添加新的物理效应

3. **自定义UI组件**
   - 扩展 `UIController.ts`
   - 添加新的控制参数

## 贡献指南

欢迎提交Issue和Pull Request来改进这个项目！

### 开发环境设置
1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 创建Pull Request

## 许可证

MIT License

## 联系方式

如有问题或建议，请通过Issue联系我们。
