import * as THREE from 'three';
import { Lens } from '../optics/Lens';
import { PointLightSource, ParallelLightSource } from '../optics/LightSource';

export interface Ray {
  origin: THREE.Vector3;
  direction: THREE.Vector3;
  intensity: number;
  color: number;
  wavelength?: number;
}

export interface RaySegment {
  start: THREE.Vector3;
  end: THREE.Vector3;
  intensity: number;
  color: number;
}

export class RayTracer {
  private rayGroup: THREE.Group;
  private maxRayLength: number = 50;
  private maxReflections: number = 5;

  constructor() {
    this.rayGroup = new THREE.Group();
    this.rayGroup.name = 'RayGroup';
  }

  public generateRays(
    lightSource: PointLightSource | ParallelLightSource,
    lenses: Lens[]
  ): RaySegment[] {
    const rays: Ray[] = [];
    const raySegments: RaySegment[] = [];

    if (lightSource instanceof PointLightSource) {
      rays.push(...this.generatePointLightRays(lightSource));
    } else if (lightSource instanceof ParallelLightSource) {
      rays.push(...this.generateParallelLightRays(lightSource));
    }

    // 追踪每条射线
    rays.forEach(ray => {
      const segments = this.traceRay(ray, lenses);
      raySegments.push(...segments);
    });

    return raySegments;
  }

  private generatePointLightRays(lightSource: PointLightSource): Ray[] {
    const rays: Ray[] = [];
    const directions = lightSource.generateRayDirections();
    const position = lightSource.getPosition();
    const intensity = lightSource.getIntensity();
    const color = lightSource.getColor();

    directions.forEach(direction => {
      rays.push({
        origin: position.clone(),
        direction: direction.clone(),
        intensity: intensity / directions.length,
        color: color
      });
    });

    return rays;
  }

  private generateParallelLightRays(lightSource: ParallelLightSource): Ray[] {
    const rays: Ray[] = [];
    const directions = lightSource.generateRayDirections();
    const positions = lightSource.generateRayPositions();
    const intensity = lightSource.getIntensity();
    const color = lightSource.getColor();

    positions.forEach((position, index) => {
      if (index < directions.length) {
        rays.push({
          origin: position.clone(),
          direction: directions[index].clone(),
          intensity: intensity / positions.length,
          color: color
        });
      }
    });

    return rays;
  }

  private traceRay(ray: Ray, lenses: Lens[]): RaySegment[] {
    const segments: RaySegment[] = [];
    let currentRay = { ...ray };
    let reflectionCount = 0;

    while (reflectionCount < this.maxReflections) {
      // 找到最近的透镜交点
      const intersection = this.findNearestLensIntersection(currentRay, lenses);
      
      if (!intersection) {
        // 没有交点，射线直接延伸到最大长度
        const endPoint = currentRay.origin.clone().add(
          currentRay.direction.clone().multiplyScalar(this.maxRayLength)
        );
        
        segments.push({
          start: currentRay.origin.clone(),
          end: endPoint,
          intensity: currentRay.intensity,
          color: currentRay.color
        });
        break;
      }

      // 添加到交点的线段
      segments.push({
        start: currentRay.origin.clone(),
        end: intersection.point.clone(),
        intensity: currentRay.intensity,
        color: currentRay.color
      });

      // 计算折射
      const refractedRay = this.calculateRefraction(
        currentRay,
        intersection.point,
        intersection.normal,
        intersection.lens
      );

      if (!refractedRay) {
        // 全反射
        break;
      }

      currentRay = refractedRay;
      reflectionCount++;
    }

    return segments;
  }

  private findNearestLensIntersection(ray: Ray, lenses: Lens[]): {
    point: THREE.Vector3;
    normal: THREE.Vector3;
    lens: Lens;
    distance: number;
  } | null {
    let nearestIntersection: any = null;
    let minDistance = Infinity;

    lenses.forEach(lens => {
      const intersection = this.rayLensIntersection(ray, lens);
      if (intersection && intersection.distance < minDistance) {
        minDistance = intersection.distance;
        nearestIntersection = { ...intersection, lens };
      }
    });

    return nearestIntersection;
  }

  private rayLensIntersection(ray: Ray, lens: Lens): {
    point: THREE.Vector3;
    normal: THREE.Vector3;
    distance: number;
  } | null {
    // 简化的射线-透镜相交检测
    // 这里使用透镜的边界球进行近似计算
    const lensPosition = lens.getPosition();
    const lensRadius = lens.getDiameter() / 2;
    
    const oc = ray.origin.clone().sub(lensPosition);
    const a = ray.direction.dot(ray.direction);
    const b = 2.0 * oc.dot(ray.direction);
    const c = oc.dot(oc) - lensRadius * lensRadius;
    
    const discriminant = b * b - 4 * a * c;
    
    if (discriminant < 0) {
      return null;
    }
    
    const t1 = (-b - Math.sqrt(discriminant)) / (2 * a);
    const t2 = (-b + Math.sqrt(discriminant)) / (2 * a);
    
    const t = t1 > 0.001 ? t1 : (t2 > 0.001 ? t2 : -1);
    
    if (t < 0) {
      return null;
    }
    
    const point = ray.origin.clone().add(ray.direction.clone().multiplyScalar(t));
    const normal = point.clone().sub(lensPosition).normalize();
    
    return {
      point,
      normal,
      distance: t
    };
  }

  private calculateRefraction(
    ray: Ray,
    intersectionPoint: THREE.Vector3,
    normal: THREE.Vector3,
    lens: Lens
  ): Ray | null {
    const n1 = 1.0; // 空气的折射率
    const n2 = lens.getRefractiveIndex();
    const ratio = n1 / n2;
    
    const cosI = -normal.dot(ray.direction);
    const sinT2 = ratio * ratio * (1.0 - cosI * cosI);
    
    if (sinT2 > 1.0) {
      // 全反射
      return null;
    }
    
    const cosT = Math.sqrt(1.0 - sinT2);
    const refractedDirection = ray.direction.clone()
      .multiplyScalar(ratio)
      .add(normal.clone().multiplyScalar(ratio * cosI - cosT))
      .normalize();
    
    return {
      origin: intersectionPoint.clone().add(refractedDirection.clone().multiplyScalar(0.001)),
      direction: refractedDirection,
      intensity: ray.intensity * 0.9, // 考虑透射损失
      color: ray.color
    };
  }

  public displayRays(scene: THREE.Scene, raySegments: RaySegment[]): void {
    // 清除之前的射线
    this.clearRays(scene);
    
    raySegments.forEach(segment => {
      const geometry = new THREE.BufferGeometry().setFromPoints([
        segment.start,
        segment.end
      ]);
      
      const material = new THREE.LineBasicMaterial({
        color: segment.color,
        transparent: true,
        opacity: Math.min(segment.intensity, 1.0)
      });
      
      const line = new THREE.Line(geometry, material);
      this.rayGroup.add(line);
    });
    
    scene.add(this.rayGroup);
  }

  public clearRays(scene: THREE.Scene): void {
    // 清理几何体和材质
    this.rayGroup.children.forEach(child => {
      if (child instanceof THREE.Line) {
        child.geometry.dispose();
        if (child.material instanceof THREE.Material) {
          child.material.dispose();
        }
      }
    });
    
    this.rayGroup.clear();
    scene.remove(this.rayGroup);
  }

  public setMaxRayLength(length: number): void {
    this.maxRayLength = length;
  }

  public setMaxReflections(count: number): void {
    this.maxReflections = count;
  }
}
