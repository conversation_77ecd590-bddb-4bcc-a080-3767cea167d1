import * as THREE from 'three';
import { LensManager } from '../optics/LensManager';
import { LightSourceManager } from '../optics/LightSourceManager';

export interface InteractionConfig {
  camera: THREE.Camera;
  scene: THREE.Scene;
  renderer: THREE.WebGLRenderer;
  lensManager: LensManager;
  lightSourceManager: LightSourceManager;
}

export class InteractionManager {
  private config: InteractionConfig;
  private raycaster: THREE.Raycaster;
  private mouse: THREE.Vector2;
  private selectedObject: THREE.Object3D | null = null;
  private isDragging: boolean = false;
  private dragPlane: THREE.Plane;
  private dragOffset: THREE.Vector3;
  private contextMenu: HTMLElement | null = null;
  private lastClickPosition: THREE.Vector3 = new THREE.Vector3();

  constructor(config: InteractionConfig) {
    this.config = config;
    this.raycaster = new THREE.Raycaster();
    this.mouse = new THREE.Vector2();
    this.dragPlane = new THREE.Plane(new THREE.Vector3(0, 0, 1), 0);
    this.dragOffset = new THREE.Vector3();
    
    this.setupEventListeners();
    this.createContextMenu();
  }

  private setupEventListeners(): void {
    const canvas = this.config.renderer.domElement;

    // 鼠标事件
    canvas.addEventListener('mousedown', this.onMouseDown.bind(this));
    canvas.addEventListener('mousemove', this.onMouseMove.bind(this));
    canvas.addEventListener('mouseup', this.onMouseUp.bind(this));
    canvas.addEventListener('contextmenu', this.onContextMenu.bind(this));
    canvas.addEventListener('dblclick', this.onDoubleClick.bind(this));

    // 键盘事件
    window.addEventListener('keydown', this.onKeyDown.bind(this));

    // 防止右键菜单
    canvas.addEventListener('contextmenu', (e) => e.preventDefault());
  }

  private updateMousePosition(event: MouseEvent): void {
    const rect = this.config.renderer.domElement.getBoundingClientRect();
    this.mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
    this.mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;
  }

  private onMouseDown(event: MouseEvent): void {
    if (event.button !== 0) return; // 只处理左键

    this.updateMousePosition(event);
    this.raycaster.setFromCamera(this.mouse, this.config.camera);

    // 检查是否点击了透镜或光源
    const lensGroup = this.config.scene.getObjectByName('LensGroup');
    const lightGroup = this.config.scene.getObjectByName('LightGroup');
    
    const intersectableObjects: THREE.Object3D[] = [];
    if (lensGroup) intersectableObjects.push(...lensGroup.children);
    if (lightGroup) intersectableObjects.push(...lightGroup.children);

    const intersects = this.raycaster.intersectObjects(intersectableObjects, true);

    if (intersects.length > 0) {
      this.selectedObject = this.findSelectableParent(intersects[0].object);
      if (this.selectedObject) {
        this.isDragging = true;
        this.calculateDragOffset(intersects[0].point);
        this.highlightObject(this.selectedObject, true);
        
        // 禁用轨道控制器
        (this.config.renderer.domElement as any).style.cursor = 'grabbing';
      }
    }
  }

  private onMouseMove(event: MouseEvent): void {
    this.updateMousePosition(event);

    if (this.isDragging && this.selectedObject) {
      this.raycaster.setFromCamera(this.mouse, this.config.camera);
      
      const intersectPoint = new THREE.Vector3();
      this.raycaster.ray.intersectPlane(this.dragPlane, intersectPoint);
      
      if (intersectPoint) {
        const newPosition = intersectPoint.sub(this.dragOffset);
        this.selectedObject.position.copy(newPosition);
        
        // 更新管理器中的位置
        this.updateObjectPosition(this.selectedObject, newPosition);
      }
    } else {
      // 悬停效果
      this.raycaster.setFromCamera(this.mouse, this.config.camera);
      const lensGroup = this.config.scene.getObjectByName('LensGroup');
      const lightGroup = this.config.scene.getObjectByName('LightGroup');
      
      const intersectableObjects: THREE.Object3D[] = [];
      if (lensGroup) intersectableObjects.push(...lensGroup.children);
      if (lightGroup) intersectableObjects.push(...lightGroup.children);

      const intersects = this.raycaster.intersectObjects(intersectableObjects, true);
      
      if (intersects.length > 0) {
        (this.config.renderer.domElement as any).style.cursor = 'grab';
      } else {
        (this.config.renderer.domElement as any).style.cursor = 'default';
      }
    }
  }

  private onMouseUp(event: MouseEvent): void {
    if (this.isDragging && this.selectedObject) {
      this.highlightObject(this.selectedObject, false);
      this.isDragging = false;
      this.selectedObject = null;
      (this.config.renderer.domElement as any).style.cursor = 'default';
    }
  }

  private onContextMenu(event: MouseEvent): void {
    event.preventDefault();
    this.updateMousePosition(event);
    
    // 计算3D世界坐标
    this.raycaster.setFromCamera(this.mouse, this.config.camera);
    const intersectPoint = new THREE.Vector3();
    this.raycaster.ray.intersectPlane(this.dragPlane, intersectPoint);
    
    if (intersectPoint) {
      this.lastClickPosition.copy(intersectPoint);
      this.showContextMenu(event.clientX, event.clientY);
    }
  }

  private onDoubleClick(event: MouseEvent): void {
    this.updateMousePosition(event);
    this.raycaster.setFromCamera(this.mouse, this.config.camera);

    const lensGroup = this.config.scene.getObjectByName('LensGroup');
    const lightGroup = this.config.scene.getObjectByName('LightGroup');
    
    const intersectableObjects: THREE.Object3D[] = [];
    if (lensGroup) intersectableObjects.push(...lensGroup.children);
    if (lightGroup) intersectableObjects.push(...lightGroup.children);

    const intersects = this.raycaster.intersectObjects(intersectableObjects, true);

    if (intersects.length > 0) {
      const object = this.findSelectableParent(intersects[0].object);
      if (object) {
        this.showObjectProperties(object);
      }
    }
  }

  private onKeyDown(event: KeyboardEvent): void {
    switch (event.code) {
      case 'Delete':
      case 'Backspace':
        if (this.selectedObject) {
          this.deleteObject(this.selectedObject);
        }
        break;
      case 'Escape':
        this.hideContextMenu();
        if (this.selectedObject) {
          this.highlightObject(this.selectedObject, false);
          this.selectedObject = null;
        }
        break;
    }
  }

  private findSelectableParent(object: THREE.Object3D): THREE.Object3D | null {
    let current = object;
    while (current) {
      if (current.userData.type === 'lens' || current.userData.type === 'lightSource') {
        return current;
      }
      current = current.parent!;
    }
    return null;
  }

  private calculateDragOffset(intersectPoint: THREE.Vector3): void {
    if (this.selectedObject) {
      this.dragOffset.copy(intersectPoint).sub(this.selectedObject.position);
    }
  }

  private highlightObject(object: THREE.Object3D, highlight: boolean): void {
    object.traverse((child) => {
      if (child instanceof THREE.Mesh) {
        if (highlight) {
          (child.material as any).emissive = new THREE.Color(0x444444);
        } else {
          (child.material as any).emissive = new THREE.Color(0x000000);
        }
      }
    });
  }

  private updateObjectPosition(object: THREE.Object3D, position: THREE.Vector3): void {
    // 根据对象类型更新相应管理器中的位置
    if (object.userData.type === 'lens') {
      // 找到对应的透镜并更新位置
      const lenses = this.config.lensManager.getLenses();
      const lensIndex = lenses.findIndex(lens => lens.getMesh() === object);
      if (lensIndex !== -1) {
        this.config.lensManager.moveLens(lensIndex, position);
      }
    } else if (object.userData.type === 'lightSource') {
      // 找到对应的光源并更新位置
      const lightSources = this.config.lightSourceManager.getLightSources();
      const lightIndex = lightSources.findIndex(light => light.getGroup() === object);
      if (lightIndex !== -1) {
        this.config.lightSourceManager.moveLightSource(lightIndex, position);
      }
    }
  }

  private createContextMenu(): void {
    this.contextMenu = document.createElement('div');
    this.contextMenu.className = 'context-menu';
    this.contextMenu.style.cssText = `
      position: fixed;
      background: rgba(40, 40, 40, 0.95);
      border: 1px solid #666;
      border-radius: 4px;
      padding: 8px 0;
      min-width: 150px;
      z-index: 10000;
      display: none;
      font-family: Arial, sans-serif;
      font-size: 14px;
      color: white;
      box-shadow: 0 4px 12px rgba(0,0,0,0.3);
    `;

    const menuItems = [
      { text: '添加凸透镜', action: () => this.addLens('convex') },
      { text: '添加凹透镜', action: () => this.addLens('concave') },
      { text: '---', action: null },
      { text: '添加点光源', action: () => this.addLightSource('point') },
      { text: '添加平行光', action: () => this.addLightSource('parallel') }
    ];

    menuItems.forEach(item => {
      if (item.text === '---') {
        const separator = document.createElement('div');
        separator.style.cssText = 'height: 1px; background: #666; margin: 4px 0;';
        this.contextMenu!.appendChild(separator);
      } else {
        const menuItem = document.createElement('div');
        menuItem.textContent = item.text;
        menuItem.style.cssText = `
          padding: 8px 16px;
          cursor: pointer;
          transition: background-color 0.2s;
        `;
        menuItem.addEventListener('mouseenter', () => {
          menuItem.style.backgroundColor = 'rgba(100, 100, 100, 0.5)';
        });
        menuItem.addEventListener('mouseleave', () => {
          menuItem.style.backgroundColor = 'transparent';
        });
        menuItem.addEventListener('click', () => {
          if (item.action) item.action();
          this.hideContextMenu();
        });
        this.contextMenu!.appendChild(menuItem);
      }
    });

    document.body.appendChild(this.contextMenu);

    // 点击其他地方隐藏菜单
    document.addEventListener('click', (e) => {
      if (!this.contextMenu!.contains(e.target as Node)) {
        this.hideContextMenu();
      }
    });
  }

  private showContextMenu(x: number, y: number): void {
    if (this.contextMenu) {
      this.contextMenu.style.left = x + 'px';
      this.contextMenu.style.top = y + 'px';
      this.contextMenu.style.display = 'block';
    }
  }

  private hideContextMenu(): void {
    if (this.contextMenu) {
      this.contextMenu.style.display = 'none';
    }
  }

  private addLens(type: 'convex' | 'concave'): void {
    this.config.lensManager.addLens({
      type: type,
      position: this.lastClickPosition.clone(),
      focalLength: type === 'convex' ? 5 : -5,
      diameter: 3,
      thickness: 0.5
    });
    
    console.log(`在位置 (${this.lastClickPosition.x.toFixed(1)}, ${this.lastClickPosition.y.toFixed(1)}, ${this.lastClickPosition.z.toFixed(1)}) 添加${type === 'convex' ? '凸' : '凹'}透镜`);
  }

  private addLightSource(type: 'point' | 'parallel'): void {
    if (type === 'point') {
      this.config.lightSourceManager.addPointLight({
        position: this.lastClickPosition.clone(),
        intensity: 1.0,
        color: 0xffffff,
        rayCount: 12
      });
    } else {
      this.config.lightSourceManager.addParallelLight({
        position: this.lastClickPosition.clone(),
        intensity: 1.0,
        color: 0xffffff,
        rayCount: 12,
        direction: new THREE.Vector3(1, 0, 0)
      });
    }
    
    console.log(`在位置 (${this.lastClickPosition.x.toFixed(1)}, ${this.lastClickPosition.y.toFixed(1)}, ${this.lastClickPosition.z.toFixed(1)}) 添加${type === 'point' ? '点' : '平行'}光源`);
  }

  private showObjectProperties(object: THREE.Object3D): void {
    // 这里可以显示对象属性编辑面板
    console.log('显示对象属性:', object.userData);
  }

  private deleteObject(object: THREE.Object3D): void {
    if (object.userData.type === 'lens') {
      const lenses = this.config.lensManager.getLenses();
      const lens = lenses.find(l => l.getMesh() === object);
      if (lens) {
        this.config.lensManager.removeLens(lens);
        console.log('删除透镜');
      }
    } else if (object.userData.type === 'lightSource') {
      const lightSources = this.config.lightSourceManager.getLightSources();
      const lightSource = lightSources.find(l => l.getGroup() === object);
      if (lightSource) {
        this.config.lightSourceManager.removeLightSource(lightSource);
        console.log('删除光源');
      }
    }
    
    this.selectedObject = null;
  }

  public dispose(): void {
    if (this.contextMenu) {
      document.body.removeChild(this.contextMenu);
    }
  }
}
