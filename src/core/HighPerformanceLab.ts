import * as THREE from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js';
import Stats from 'stats.js';
import { GUI } from 'lil-gui';

export class HighPerformanceLab {
  private renderer!: THREE.WebGLRenderer;
  private scene!: THREE.Scene;
  private camera!: THREE.PerspectiveCamera;
  private controls!: OrbitControls;
  private stats!: Stats;
  private gui!: GUI;
  
  private animationId: number = 0;
  private isInitialized: boolean = false;
  private isRunning: boolean = false;
  private frameCount: number = 0;
  private lastTime: number = 0;
  private targetFPS: number = 60;

  constructor() {
    console.log('🚀 初始化高性能虚拟实验室...');
  }

  public init(): void {
    if (this.isInitialized) {
      console.warn('虚拟实验室已经初始化');
      return;
    }

    try {
      this.setupRenderer();
      this.setupScene();
      this.setupCamera();
      this.setupControls();
      this.setupStats();
      this.setupGUI();
      this.setupEventListeners();
      this.createOptimizedScene();
      
      this.isInitialized = true;
      console.log('✅ 高性能虚拟实验室初始化完成');
    } catch (error) {
      console.error('❌ 初始化失败:', error);
      this.cleanup();
    }
  }

  private setupRenderer(): void {
    try {
      // 优化的WebGL渲染器设置
      this.renderer = new THREE.WebGLRenderer({ 
        antialias: false, // 关闭抗锯齿以提高性能
        alpha: false,     // 关闭透明度以提高性能
        powerPreference: "high-performance", // 使用高性能GPU
        stencil: false,   // 关闭模板缓冲
        depth: true,      // 保留深度缓冲
        logarithmicDepthBuffer: false // 关闭对数深度缓冲
      });
      
      this.renderer.setSize(window.innerWidth, window.innerHeight);
      this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 1.5)); // 限制像素比
      
      // 优化渲染设置
      this.renderer.shadowMap.enabled = false; // 关闭阴影以提高性能
      this.renderer.outputColorSpace = THREE.SRGBColorSpace;
      this.renderer.toneMapping = THREE.NoToneMapping; // 使用最简单的色调映射
      
      // 设置清除颜色
      this.renderer.setClearColor(0x1a1a1a, 1.0);
      
      // WebGL上下文优化
      const gl = this.renderer.getContext();
      if (gl) {
        console.log('✓ WebGL上下文信息:');
        console.log(`  - 版本: ${gl.getParameter(gl.VERSION)}`);
        console.log(`  - 渲染器: ${gl.getParameter(gl.RENDERER)}`);
        console.log(`  - 供应商: ${gl.getParameter(gl.VENDOR)}`);
      }
      
      const container = document.getElementById('app');
      if (container) {
        container.appendChild(this.renderer.domElement);
      }
      
      console.log('✓ 高性能渲染器设置成功');
    } catch (error) {
      console.error('❌ 渲染器设置失败:', error);
      throw error;
    }
  }

  private setupScene(): void {
    this.scene = new THREE.Scene();
    this.scene.background = new THREE.Color(0x1a1a1a);
    
    // 优化场景设置
    this.scene.matrixAutoUpdate = false; // 手动更新矩阵以提高性能
    
    console.log('✓ 优化场景创建成功');
  }

  private setupCamera(): void {
    this.camera = new THREE.PerspectiveCamera(
      60, // 减小FOV以提高性能
      window.innerWidth / window.innerHeight,
      0.1,
      100 // 减小远裁剪面以提高性能
    );
    this.camera.position.set(0, 5, 10);
    this.camera.lookAt(0, 0, 0);
    this.camera.updateProjectionMatrix();
    
    console.log('✓ 优化相机设置成功');
  }

  private setupControls(): void {
    this.controls = new OrbitControls(this.camera, this.renderer.domElement);
    this.controls.enableDamping = true;
    this.controls.dampingFactor = 0.1; // 增加阻尼以减少更新频率
    this.controls.maxDistance = 30;
    this.controls.minDistance = 2;
    this.controls.enablePan = true;
    this.controls.enableZoom = true;
    this.controls.enableRotate = true;
    
    // 优化控制器更新
    this.controls.addEventListener('change', () => {
      // 只在需要时渲染
    });
    
    console.log('✓ 优化控制器设置成功');
  }

  private setupStats(): void {
    this.stats = new Stats();
    this.stats.showPanel(0); // 显示FPS
    this.stats.dom.style.position = 'absolute';
    this.stats.dom.style.top = '0px';
    this.stats.dom.style.left = '0px';
    this.stats.dom.style.zIndex = '100';
    document.body.appendChild(this.stats.dom);
    
    console.log('✓ 性能监控设置成功');
  }

  private setupGUI(): void {
    this.gui = new GUI({ title: '高性能虚拟实验室' });
    
    const params = {
      targetFPS: this.targetFPS,
      antialias: false,
      shadows: false,
      wireframe: false,
      resetCamera: () => {
        this.camera.position.set(0, 5, 10);
        this.camera.lookAt(0, 0, 0);
        this.controls.reset();
      },
      restart: () => {
        this.restart();
      }
    };

    // 性能控制
    const performanceFolder = this.gui.addFolder('性能控制');
    performanceFolder.add(params, 'targetFPS', 30, 120, 1).name('目标FPS').onChange((value: number) => {
      this.targetFPS = value;
    });
    performanceFolder.add(params, 'antialias').name('抗锯齿').onChange((value: boolean) => {
      console.log('抗锯齿设置需要重启渲染器');
    });
    performanceFolder.add(params, 'shadows').name('阴影').onChange((value: boolean) => {
      this.renderer.shadowMap.enabled = value;
    });
    performanceFolder.add(params, 'wireframe').name('线框模式').onChange((value: boolean) => {
      this.scene.traverse((child) => {
        if (child instanceof THREE.Mesh && child.material instanceof THREE.Material) {
          (child.material as any).wireframe = value;
        }
      });
    });

    // 操作控制
    const controlFolder = this.gui.addFolder('操作');
    controlFolder.add(params, 'resetCamera').name('重置相机');
    controlFolder.add(params, 'restart').name('重启实验室');
    
    console.log('✓ GUI设置成功');
  }

  private setupEventListeners(): void {
    window.addEventListener('resize', this.onWindowResize.bind(this));
    
    window.addEventListener('keydown', (event) => {
      switch (event.code) {
        case 'KeyR':
          this.camera.position.set(0, 5, 10);
          this.camera.lookAt(0, 0, 0);
          this.controls.reset();
          break;
        case 'KeyH':
          if (this.gui.domElement.style.display === 'none') {
            this.gui.show();
          } else {
            this.gui.hide();
          }
          break;
        case 'KeyS':
          this.restart();
          break;
        case 'KeyF':
          this.toggleFullscreen();
          break;
      }
    });
    
    console.log('✓ 事件监听器设置成功');
  }

  private createOptimizedScene(): void {
    // 优化的光照设置
    const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
    this.scene.add(ambientLight);
    
    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(10, 10, 5);
    directionalLight.castShadow = false; // 关闭阴影
    this.scene.add(directionalLight);
    
    // 简化的网格
    const gridHelper = new THREE.GridHelper(20, 10, 0x444444, 0x222222); // 减少网格线数量
    gridHelper.position.y = -2;
    this.scene.add(gridHelper);
    
    // 简化的坐标轴
    const axesHelper = new THREE.AxesHelper(3);
    this.scene.add(axesHelper);
    
    // 优化的透镜几何体
    const lensGeometry = new THREE.CylinderGeometry(1.5, 1.5, 0.3, 16); // 减少分段数
    const lensMaterial = new THREE.MeshLambertMaterial({ // 使用更简单的材质
      color: 0x88ccff,
      transparent: true,
      opacity: 0.7
    });
    
    const lens = new THREE.Mesh(lensGeometry, lensMaterial);
    lens.position.set(0, 0, 0);
    lens.rotation.z = Math.PI / 2;
    lens.matrixAutoUpdate = false; // 禁用自动矩阵更新
    lens.updateMatrix();
    this.scene.add(lens);
    
    // 优化的光线
    const rayMaterial = new THREE.LineBasicMaterial({ 
      color: 0xffff00,
      transparent: false, // 关闭透明度
      opacity: 1.0
    });

    for (let i = -2; i <= 2; i++) {
      const points = [
        new THREE.Vector3(-8, i * 0.5, 0),
        new THREE.Vector3(8, i * 0.5, 0)
      ];
      
      const rayGeometry = new THREE.BufferGeometry().setFromPoints(points);
      const ray = new THREE.Line(rayGeometry, rayMaterial);
      ray.matrixAutoUpdate = false;
      ray.updateMatrix();
      this.scene.add(ray);
    }
    
    console.log('✓ 优化场景创建成功');
  }

  private onWindowResize(): void {
    if (!this.camera || !this.renderer) return;
    
    this.camera.aspect = window.innerWidth / window.innerHeight;
    this.camera.updateProjectionMatrix();
    this.renderer.setSize(window.innerWidth, window.innerHeight);
  }

  private toggleFullscreen(): void {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen();
    } else {
      document.exitFullscreen();
    }
  }

  public start(): void {
    if (!this.isInitialized) {
      console.error('请先调用 init() 方法');
      return;
    }
    
    if (this.isRunning) {
      console.warn('虚拟实验室已在运行');
      return;
    }
    
    this.isRunning = true;
    this.lastTime = performance.now();
    this.animate();
    console.log('✓ 高性能虚拟实验室启动成功');
  }

  private animate(): void {
    if (!this.isRunning) return;

    // 始终请求下一帧
    this.animationId = requestAnimationFrame(this.animate.bind(this));

    try {
      this.stats.begin();

      // 更新控制器
      if (this.controls) {
        this.controls.update();
      }

      // 渲染场景
      if (this.renderer && this.scene && this.camera) {
        this.renderer.render(this.scene, this.camera);
      }

      this.stats.end();
      this.frameCount++;

      // 每秒输出一次性能信息
      if (this.frameCount % 60 === 0) {
        const info = this.renderer.info;
        console.log(`性能信息 - 几何体: ${info.memory.geometries}, 纹理: ${info.memory.textures}, 绘制调用: ${info.render.calls}`);
      }

    } catch (error) {
      console.error('渲染错误:', error);
      this.stop();
    }
  }

  public stop(): void {
    this.isRunning = false;
    if (this.animationId) {
      cancelAnimationFrame(this.animationId);
      this.animationId = 0;
    }
    console.log('✓ 虚拟实验室已停止');
  }

  public restart(): void {
    console.log('🔄 重启虚拟实验室...');
    this.stop();
    setTimeout(() => {
      this.start();
    }, 100);
  }

  private cleanup(): void {
    this.stop();
    
    if (this.renderer) {
      this.renderer.dispose();
    }
    
    if (this.gui) {
      this.gui.destroy();
    }
    
    if (this.stats && this.stats.dom && this.stats.dom.parentNode) {
      this.stats.dom.remove();
    }
  }

  public dispose(): void {
    this.cleanup();
    this.isInitialized = false;
    console.log('✓ 虚拟实验室已清理');
  }
}
