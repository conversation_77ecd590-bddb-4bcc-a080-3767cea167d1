import * as THREE from 'three';

export class SceneManager {
  private scene: THREE.Scene;
  private gridHelper!: THREE.GridHelper;
  private axesHelper!: THREE.AxesHelper;
  private ambientLight!: THREE.AmbientLight;
  private directionalLight!: THREE.DirectionalLight;

  constructor(scene: THREE.Scene) {
    this.scene = scene;
  }

  public init(): void {
    this.setupLighting();
    this.setupHelpers();
    this.setupEnvironment();
  }

  private setupLighting(): void {
    // 环境光
    this.ambientLight = new THREE.AmbientLight(0x404040, 0.3);
    this.scene.add(this.ambientLight);

    // 方向光
    this.directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    this.directionalLight.position.set(10, 10, 5);
    this.directionalLight.castShadow = true;
    this.directionalLight.shadow.mapSize.width = 2048;
    this.directionalLight.shadow.mapSize.height = 2048;
    this.directionalLight.shadow.camera.near = 0.5;
    this.directionalLight.shadow.camera.far = 50;
    this.directionalLight.shadow.camera.left = -20;
    this.directionalLight.shadow.camera.right = 20;
    this.directionalLight.shadow.camera.top = 20;
    this.directionalLight.shadow.camera.bottom = -20;
    this.scene.add(this.directionalLight);
  }

  private setupHelpers(): void {
    // 网格辅助线
    this.gridHelper = new THREE.GridHelper(20, 20, 0x444444, 0x222222);
    this.gridHelper.position.y = -2;
    this.scene.add(this.gridHelper);

    // 坐标轴辅助线
    this.axesHelper = new THREE.AxesHelper(5);
    this.scene.add(this.axesHelper);
  }

  private setupEnvironment(): void {
    // 添加实验台
    const tableGeometry = new THREE.BoxGeometry(20, 0.2, 8);
    const tableMaterial = new THREE.MeshLambertMaterial({ 
      color: 0x8B4513,
      transparent: true,
      opacity: 0.8
    });
    const table = new THREE.Mesh(tableGeometry, tableMaterial);
    table.position.y = -2;
    table.receiveShadow = true;
    this.scene.add(table);

    // 添加背景墙
    const wallGeometry = new THREE.PlaneGeometry(30, 15);
    const wallMaterial = new THREE.MeshLambertMaterial({ 
      color: 0x2c2c2c,
      transparent: true,
      opacity: 0.5
    });
    const wall = new THREE.Mesh(wallGeometry, wallMaterial);
    wall.position.set(0, 5, -10);
    wall.receiveShadow = true;
    this.scene.add(wall);
  }

  public setGridVisible(visible: boolean): void {
    this.gridHelper.visible = visible;
  }

  public setAxesVisible(visible: boolean): void {
    this.axesHelper.visible = visible;
  }

  public setAmbientLightIntensity(intensity: number): void {
    this.ambientLight.intensity = intensity;
  }

  public setDirectionalLightIntensity(intensity: number): void {
    this.directionalLight.intensity = intensity;
  }
}
