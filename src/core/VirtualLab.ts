import * as THREE from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js';
import Stats from 'stats.js';
import { GUI } from 'lil-gui';
import { SceneManager } from './SceneManager';
import { LensManager } from '../optics/LensManager';
import { LightSourceManager } from '../optics/LightSourceManager';
import { RayTracer } from '../physics/RayTracer';
import { UIController } from '../ui/UIController';
import { InteractionManager } from '../interaction/InteractionManager';

export class VirtualLab {
  private renderer!: THREE.WebGLRenderer;
  private scene!: THREE.Scene;
  private camera!: THREE.PerspectiveCamera;
  private controls!: OrbitControls;
  private stats!: Stats;
  private gui!: GUI;
  
  private sceneManager!: SceneManager;
  private lensManager!: LensManager;
  private lightSourceManager!: LightSourceManager;
  private rayTracer!: RayTracer;
  private uiController!: UIController;
  private interactionManager!: InteractionManager;
  
  private animationId: number = 0;
  private isInitialized: boolean = false;

  constructor() {
    this.setupRenderer();
    this.setupScene();
    this.setupCamera();
    this.setupControls();
    this.setupStats();
    this.setupManagers();
  }

  private setupRenderer(): void {
    this.renderer = new THREE.WebGLRenderer({
      antialias: true,
      alpha: true
    });
    this.renderer.setSize(window.innerWidth, window.innerHeight);
    this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
    this.renderer.shadowMap.enabled = true;
    this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
    this.renderer.outputColorSpace = THREE.SRGBColorSpace;
    this.renderer.toneMapping = THREE.ACESFilmicToneMapping;
    this.renderer.toneMappingExposure = 1.0;

    // 添加WebGL上下文丢失处理
    this.renderer.domElement.addEventListener('webglcontextlost', this.onContextLost.bind(this));
    this.renderer.domElement.addEventListener('webglcontextrestored', this.onContextRestored.bind(this));

    const container = document.getElementById('app');
    if (container) {
      container.appendChild(this.renderer.domElement);
    }
  }

  private setupScene(): void {
    this.scene = new THREE.Scene();
    this.scene.background = new THREE.Color(0x0a0a0a);
    this.scene.fog = new THREE.Fog(0x0a0a0a, 50, 200);
  }

  private setupCamera(): void {
    this.camera = new THREE.PerspectiveCamera(
      75,
      window.innerWidth / window.innerHeight,
      0.1,
      1000
    );
    this.camera.position.set(0, 5, 10);
    this.camera.lookAt(0, 0, 0);
  }

  private setupControls(): void {
    this.controls = new OrbitControls(this.camera, this.renderer.domElement);
    this.controls.enableDamping = true;
    this.controls.dampingFactor = 0.05;
    this.controls.maxDistance = 50;
    this.controls.minDistance = 2;
    this.controls.maxPolarAngle = Math.PI * 0.8;
  }

  private setupStats(): void {
    this.stats = new Stats();
    this.stats.showPanel(0);
    document.body.appendChild(this.stats.dom);
  }

  private setupManagers(): void {
    this.sceneManager = new SceneManager(this.scene);
    this.lensManager = new LensManager(this.scene);
    this.lightSourceManager = new LightSourceManager(this.scene);
    this.rayTracer = new RayTracer();
    this.uiController = new UIController();
  }

  public init(): void {
    if (this.isInitialized) return;

    // 初始化场景
    this.sceneManager.init();
    
    // 设置GUI
    this.setupGUI();

    // 设置交互管理器
    this.setupInteractionManager();

    // 添加事件监听器
    this.setupEventListeners();

    // 创建默认场景
    this.createDefaultScene();
    
    this.isInitialized = true;
    console.log('虚拟实验室初始化完成');
  }

  private setupGUI(): void {
    this.gui = new GUI({ title: '虚拟光学实验室' });
    this.uiController.init(this.gui, {
      lensManager: this.lensManager,
      lightSourceManager: this.lightSourceManager,
      rayTracer: this.rayTracer,
      scene: this.scene
    });
  }

  private setupInteractionManager(): void {
    this.interactionManager = new InteractionManager({
      camera: this.camera,
      scene: this.scene,
      renderer: this.renderer,
      lensManager: this.lensManager,
      lightSourceManager: this.lightSourceManager
    });

    console.log('✓ 交互管理器初始化完成');
  }

  private setupEventListeners(): void {
    window.addEventListener('resize', this.onWindowResize.bind(this));
    
    // 键盘事件
    window.addEventListener('keydown', (event) => {
      switch (event.code) {
        case 'KeyR':
          this.resetScene();
          break;
        case 'KeyH':
          this.toggleGUI();
          break;
      }
    });
  }

  private createDefaultScene(): void {
    // 添加默认凸透镜
    this.lensManager.addLens({
      type: 'convex',
      position: new THREE.Vector3(0, 0, 0),
      focalLength: 5,
      diameter: 3,
      thickness: 0.5
    });

    // 添加默认点光源
    this.lightSourceManager.addPointLight({
      position: new THREE.Vector3(-8, 0, 0),
      intensity: 1.0,
      color: 0xffffff
    });
  }

  private onWindowResize(): void {
    this.camera.aspect = window.innerWidth / window.innerHeight;
    this.camera.updateProjectionMatrix();
    this.renderer.setSize(window.innerWidth, window.innerHeight);
  }

  private resetScene(): void {
    this.lensManager.clear();
    this.lightSourceManager.clear();
    this.createDefaultScene();
  }

  private toggleGUI(): void {
    if (this.gui.domElement.style.display === 'none') {
      this.gui.show();
    } else {
      this.gui.hide();
    }
  }

  public start(): void {
    if (!this.isInitialized) {
      console.error('请先调用 init() 方法');
      return;
    }
    this.animate();
  }

  private animate(): void {
    this.animationId = requestAnimationFrame(this.animate.bind(this));

    // 检查必要组件是否已初始化
    if (!this.stats || !this.controls || !this.renderer || !this.scene || !this.camera) {
      console.warn('VirtualLab组件未完全初始化，跳过此帧');
      return;
    }

    try {
      this.stats.begin();

      // 更新控制器
      this.controls.update();

      // 更新光线追踪
      this.updateRayTracing();

      // 渲染场景
      this.renderer.render(this.scene, this.camera);

      this.stats.end();
    } catch (error) {
      console.error('渲染过程中发生错误:', error);
      // 如果是WebGL上下文丢失，停止动画循环
      if (error instanceof Error && error.message.includes('context')) {
        console.warn('WebGL上下文问题，暂停渲染');
        return;
      }
    }
  }

  private onContextLost(event: Event): void {
    event.preventDefault();
    console.warn('WebGL上下文丢失');
    this.stop();
  }

  private onContextRestored(): void {
    console.log('WebGL上下文恢复');
    // 重新启动渲染循环
    this.start();
  }

  private updateRayTracing(): void {
    // 检查管理器是否已初始化
    if (!this.lensManager || !this.lightSourceManager || !this.rayTracer || !this.uiController) {
      return;
    }

    const lenses = this.lensManager.getLenses();
    const lightSources = this.lightSourceManager.getLightSources();

    // 清除之前的光线
    this.rayTracer.clearRays(this.scene);

    // 为每个光源计算光线
    lightSources.forEach(lightSource => {
      const rays = this.rayTracer.generateRays(lightSource, lenses);
      this.rayTracer.displayRays(this.scene, rays);
    });

    // 更新UI计数（每60帧更新一次）
    if (this.animationId % 60 === 0) {
      this.uiController.refreshCounts();
    }
  }

  public stop(): void {
    if (this.animationId) {
      cancelAnimationFrame(this.animationId);
      this.animationId = 0;
    }
  }

  public dispose(): void {
    this.stop();

    // 安全地清理资源
    if (this.renderer) {
      this.renderer.dispose();
    }

    if (this.gui) {
      this.gui.destroy();
    }

    if (this.stats && this.stats.dom && this.stats.dom.parentNode) {
      this.stats.dom.remove();
    }

    if (this.interactionManager) {
      this.interactionManager.dispose();
    }

    if (this.lensManager) {
      this.lensManager.dispose();
    }

    if (this.lightSourceManager) {
      this.lightSourceManager.dispose();
    }

    if (this.rayTracer) {
      this.rayTracer.clearRays(this.scene);
    }
  }
}
