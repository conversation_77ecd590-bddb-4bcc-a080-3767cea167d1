import * as THREE from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js';
import Stats from 'stats.js';

export class UltraFastLab {
  private renderer!: THREE.WebGLRenderer;
  private scene!: THREE.Scene;
  private camera!: THREE.PerspectiveCamera;
  private controls!: OrbitControls;
  private stats!: Stats;
  
  private animationId: number = 0;
  private isRunning: boolean = false;

  constructor() {
    console.log('⚡ 初始化超高性能实验室...');
    this.init();
  }

  private init(): void {
    try {
      this.setupRenderer();
      this.setupScene();
      this.setupCamera();
      this.setupControls();
      this.setupStats();
      this.createSimpleScene();
      this.setupEvents();
      
      console.log('✅ 超高性能实验室初始化完成');
    } catch (error) {
      console.error('❌ 初始化失败:', error);
    }
  }

  private setupRenderer(): void {
    // 最简化的渲染器配置
    this.renderer = new THREE.WebGLRenderer({ 
      antialias: false,
      alpha: false,
      powerPreference: "high-performance",
      stencil: false,
      depth: true,
      premultipliedAlpha: false,
      preserveDrawingBuffer: false
    });
    
    this.renderer.setSize(window.innerWidth, window.innerHeight);
    this.renderer.setPixelRatio(1); // 固定像素比为1
    this.renderer.shadowMap.enabled = false;
    this.renderer.autoClear = true;
    this.renderer.sortObjects = false; // 关闭对象排序
    
    // 设置清除颜色
    this.renderer.setClearColor(0x000000, 1.0);
    
    document.getElementById('app')?.appendChild(this.renderer.domElement);
    
    console.log('✓ 超简化渲染器设置完成');
  }

  private setupScene(): void {
    this.scene = new THREE.Scene();
    this.scene.autoUpdate = false; // 关闭自动更新
    this.scene.matrixAutoUpdate = false;
    console.log('✓ 超简化场景创建完成');
  }

  private setupCamera(): void {
    this.camera = new THREE.PerspectiveCamera(50, window.innerWidth / window.innerHeight, 0.1, 50);
    this.camera.position.set(0, 2, 8);
    this.camera.lookAt(0, 0, 0);
    this.camera.updateProjectionMatrix();
    console.log('✓ 相机设置完成');
  }

  private setupControls(): void {
    this.controls = new OrbitControls(this.camera, this.renderer.domElement);
    this.controls.enableDamping = false; // 关闭阻尼
    this.controls.enablePan = true;
    this.controls.enableZoom = true;
    this.controls.enableRotate = true;
    this.controls.autoRotate = false;
    console.log('✓ 控制器设置完成');
  }

  private setupStats(): void {
    this.stats = new Stats();
    this.stats.showPanel(0);
    this.stats.dom.style.position = 'absolute';
    this.stats.dom.style.top = '0px';
    this.stats.dom.style.left = '0px';
    document.body.appendChild(this.stats.dom);
    console.log('✓ 性能监控设置完成');
  }

  private createSimpleScene(): void {
    // 最简单的光照
    const light = new THREE.AmbientLight(0xffffff, 1.0);
    this.scene.add(light);
    
    // 简单的网格
    const gridHelper = new THREE.GridHelper(10, 10, 0x444444, 0x444444);
    gridHelper.position.y = -1;
    gridHelper.matrixAutoUpdate = false;
    gridHelper.updateMatrix();
    this.scene.add(gridHelper);
    
    // 简单的透镜
    const geometry = new THREE.BoxGeometry(0.2, 2, 2); // 使用Box代替Cylinder
    const material = new THREE.MeshBasicMaterial({ 
      color: 0x00ff88,
      transparent: false,
      wireframe: false
    });
    
    const lens = new THREE.Mesh(geometry, material);
    lens.position.set(0, 0, 0);
    lens.matrixAutoUpdate = false;
    lens.updateMatrix();
    this.scene.add(lens);
    
    // 简单的光线
    const lineMaterial = new THREE.LineBasicMaterial({ color: 0xffff00 });
    
    for (let i = -1; i <= 1; i++) {
      const points = [
        new THREE.Vector3(-5, i, 0),
        new THREE.Vector3(5, i, 0)
      ];
      
      const lineGeometry = new THREE.BufferGeometry().setFromPoints(points);
      const line = new THREE.Line(lineGeometry, lineMaterial);
      line.matrixAutoUpdate = false;
      line.updateMatrix();
      this.scene.add(line);
    }
    
    console.log('✓ 超简化场景创建完成');
  }

  private setupEvents(): void {
    window.addEventListener('resize', () => {
      this.camera.aspect = window.innerWidth / window.innerHeight;
      this.camera.updateProjectionMatrix();
      this.renderer.setSize(window.innerWidth, window.innerHeight);
    });
    
    window.addEventListener('keydown', (event) => {
      switch (event.code) {
        case 'KeyR':
          this.camera.position.set(0, 2, 8);
          this.camera.lookAt(0, 0, 0);
          this.controls.reset();
          break;
        case 'KeyS':
          this.restart();
          break;
        case 'Space':
          event.preventDefault();
          if (this.isRunning) {
            this.stop();
          } else {
            this.start();
          }
          break;
      }
    });
    
    console.log('✓ 事件监听器设置完成');
  }

  public start(): void {
    if (this.isRunning) {
      console.warn('实验室已在运行');
      return;
    }
    
    this.isRunning = true;
    this.animate();
    console.log('🚀 超高性能实验室启动 - 目标60FPS');
  }

  private animate(): void {
    if (!this.isRunning) return;
    
    // 立即请求下一帧
    this.animationId = requestAnimationFrame(() => this.animate());
    
    // 开始性能监控
    this.stats.begin();
    
    // 更新控制器
    this.controls.update();
    
    // 渲染场景
    this.renderer.render(this.scene, this.camera);
    
    // 结束性能监控
    this.stats.end();
  }

  public stop(): void {
    this.isRunning = false;
    if (this.animationId) {
      cancelAnimationFrame(this.animationId);
      this.animationId = 0;
    }
    console.log('⏹️ 实验室已停止');
  }

  public restart(): void {
    console.log('🔄 重启实验室...');
    this.stop();
    setTimeout(() => this.start(), 50);
  }

  public dispose(): void {
    this.stop();
    
    if (this.renderer) {
      this.renderer.dispose();
    }
    
    if (this.stats && this.stats.dom && this.stats.dom.parentNode) {
      this.stats.dom.remove();
    }
    
    console.log('✓ 实验室已清理');
  }

  // 公共方法用于外部控制
  public getRenderer(): THREE.WebGLRenderer {
    return this.renderer;
  }

  public getScene(): THREE.Scene {
    return this.scene;
  }

  public getCamera(): THREE.PerspectiveCamera {
    return this.camera;
  }

  public getStats(): Stats {
    return this.stats;
  }

  public isRunningState(): boolean {
    return this.isRunning;
  }

  // 添加简单的对象到场景
  public addSimpleObject(color: number = 0xff0000): THREE.Mesh {
    const geometry = new THREE.BoxGeometry(0.5, 0.5, 0.5);
    const material = new THREE.MeshBasicMaterial({ color });
    const mesh = new THREE.Mesh(geometry, material);
    
    mesh.position.set(
      (Math.random() - 0.5) * 4,
      (Math.random() - 0.5) * 2,
      (Math.random() - 0.5) * 4
    );
    
    mesh.matrixAutoUpdate = false;
    mesh.updateMatrix();
    
    this.scene.add(mesh);
    return mesh;
  }

  // 清除所有动态对象
  public clearDynamicObjects(): void {
    const objectsToRemove: THREE.Object3D[] = [];
    
    this.scene.traverse((child) => {
      if (child instanceof THREE.Mesh && child.geometry instanceof THREE.BoxGeometry) {
        if (child.geometry.parameters.width === 0.5) { // 识别动态添加的对象
          objectsToRemove.push(child);
        }
      }
    });
    
    objectsToRemove.forEach(obj => {
      this.scene.remove(obj);
      if (obj instanceof THREE.Mesh) {
        obj.geometry.dispose();
        if (obj.material instanceof THREE.Material) {
          obj.material.dispose();
        }
      }
    });
    
    console.log(`清除了 ${objectsToRemove.length} 个动态对象`);
  }
}
