import * as THREE from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js';
import Stats from 'stats.js';
import { GUI } from 'lil-gui';

export class SafeVirtualLab {
  private renderer!: THREE.WebGLRenderer;
  private scene!: THREE.Scene;
  private camera!: THREE.PerspectiveCamera;
  private controls!: OrbitControls;
  private stats!: Stats;
  private gui!: GUI;
  
  private animationId: number = 0;
  private isInitialized: boolean = false;
  private isRunning: boolean = false;

  constructor() {
    console.log('🔧 初始化安全版虚拟实验室...');
  }

  public init(): void {
    if (this.isInitialized) {
      console.warn('虚拟实验室已经初始化');
      return;
    }

    try {
      this.setupRenderer();
      this.setupScene();
      this.setupCamera();
      this.setupControls();
      this.setupStats();
      this.setupGUI();
      this.setupEventListeners();
      this.createDefaultScene();
      
      this.isInitialized = true;
      console.log('✅ 安全版虚拟实验室初始化完成');
    } catch (error) {
      console.error('❌ 初始化失败:', error);
      this.cleanup();
    }
  }

  private setupRenderer(): void {
    try {
      this.renderer = new THREE.WebGLRenderer({ 
        antialias: true,
        alpha: true,
        preserveDrawingBuffer: true
      });
      this.renderer.setSize(window.innerWidth, window.innerHeight);
      this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
      this.renderer.shadowMap.enabled = true;
      this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
      
      // 添加WebGL上下文事件监听
      this.renderer.domElement.addEventListener('webglcontextlost', (event) => {
        event.preventDefault();
        console.warn('WebGL上下文丢失');
        this.stop();
      });
      
      this.renderer.domElement.addEventListener('webglcontextrestored', () => {
        console.log('WebGL上下文恢复');
        this.start();
      });
      
      const container = document.getElementById('app');
      if (container) {
        container.appendChild(this.renderer.domElement);
      }
      
      console.log('✓ 渲染器设置成功');
    } catch (error) {
      console.error('❌ 渲染器设置失败:', error);
      throw error;
    }
  }

  private setupScene(): void {
    this.scene = new THREE.Scene();
    this.scene.background = new THREE.Color(0x1a1a1a);
    console.log('✓ 场景创建成功');
  }

  private setupCamera(): void {
    this.camera = new THREE.PerspectiveCamera(
      75,
      window.innerWidth / window.innerHeight,
      0.1,
      1000
    );
    this.camera.position.set(0, 5, 10);
    this.camera.lookAt(0, 0, 0);
    console.log('✓ 相机设置成功');
  }

  private setupControls(): void {
    this.controls = new OrbitControls(this.camera, this.renderer.domElement);
    this.controls.enableDamping = true;
    this.controls.dampingFactor = 0.05;
    this.controls.maxDistance = 50;
    this.controls.minDistance = 2;
    console.log('✓ 控制器设置成功');
  }

  private setupStats(): void {
    this.stats = new Stats();
    this.stats.showPanel(0);
    document.body.appendChild(this.stats.dom);
    console.log('✓ 性能监控设置成功');
  }

  private setupGUI(): void {
    this.gui = new GUI({ title: '安全版虚拟实验室' });
    
    const params = {
      resetCamera: () => {
        this.camera.position.set(0, 5, 10);
        this.camera.lookAt(0, 0, 0);
        this.controls.reset();
      },
      restart: () => {
        this.restart();
      }
    };

    this.gui.add(params, 'resetCamera').name('重置相机');
    this.gui.add(params, 'restart').name('重启实验室');
    
    console.log('✓ GUI设置成功');
  }

  private setupEventListeners(): void {
    window.addEventListener('resize', this.onWindowResize.bind(this));
    
    window.addEventListener('keydown', (event) => {
      switch (event.code) {
        case 'KeyR':
          this.camera.position.set(0, 5, 10);
          this.camera.lookAt(0, 0, 0);
          this.controls.reset();
          break;
        case 'KeyH':
          if (this.gui.domElement.style.display === 'none') {
            this.gui.show();
          } else {
            this.gui.hide();
          }
          break;
        case 'KeyS':
          this.restart();
          break;
      }
    });
    
    console.log('✓ 事件监听器设置成功');
  }

  private createDefaultScene(): void {
    // 添加基础光照
    const ambientLight = new THREE.AmbientLight(0x404040, 0.4);
    this.scene.add(ambientLight);
    
    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(10, 10, 5);
    directionalLight.castShadow = true;
    this.scene.add(directionalLight);
    
    // 添加网格
    const gridHelper = new THREE.GridHelper(20, 20, 0x444444, 0x222222);
    gridHelper.position.y = -2;
    this.scene.add(gridHelper);
    
    // 添加坐标轴
    const axesHelper = new THREE.AxesHelper(5);
    this.scene.add(axesHelper);
    
    // 添加一个简单的透镜示例
    const lensGeometry = new THREE.CylinderGeometry(1.5, 1.5, 0.3, 32);
    const lensMaterial = new THREE.MeshPhysicalMaterial({
      color: 0x88ccff,
      transparent: true,
      opacity: 0.6,
      transmission: 0.8
    });
    
    const lens = new THREE.Mesh(lensGeometry, lensMaterial);
    lens.position.set(0, 0, 0);
    lens.rotation.z = Math.PI / 2;
    this.scene.add(lens);
    
    // 添加一些光线示例
    const rayMaterial = new THREE.LineBasicMaterial({ 
      color: 0xffff00,
      transparent: true,
      opacity: 0.8
    });

    for (let i = -2; i <= 2; i++) {
      const points = [
        new THREE.Vector3(-8, i * 0.5, 0),
        new THREE.Vector3(8, i * 0.5, 0)
      ];
      
      const rayGeometry = new THREE.BufferGeometry().setFromPoints(points);
      const ray = new THREE.Line(rayGeometry, rayMaterial);
      this.scene.add(ray);
    }
    
    console.log('✓ 默认场景创建成功');
  }

  private onWindowResize(): void {
    if (!this.camera || !this.renderer) return;
    
    this.camera.aspect = window.innerWidth / window.innerHeight;
    this.camera.updateProjectionMatrix();
    this.renderer.setSize(window.innerWidth, window.innerHeight);
  }

  public start(): void {
    if (!this.isInitialized) {
      console.error('请先调用 init() 方法');
      return;
    }
    
    if (this.isRunning) {
      console.warn('虚拟实验室已在运行');
      return;
    }
    
    this.isRunning = true;
    this.animate();
    console.log('✓ 虚拟实验室启动成功');
  }

  private animate(): void {
    if (!this.isRunning) return;
    
    this.animationId = requestAnimationFrame(this.animate.bind(this));
    
    try {
      if (this.stats) this.stats.begin();
      
      if (this.controls) this.controls.update();
      
      if (this.renderer && this.scene && this.camera) {
        this.renderer.render(this.scene, this.camera);
      }
      
      if (this.stats) this.stats.end();
    } catch (error) {
      console.error('渲染错误:', error);
      this.stop();
    }
  }

  public stop(): void {
    this.isRunning = false;
    if (this.animationId) {
      cancelAnimationFrame(this.animationId);
      this.animationId = 0;
    }
    console.log('✓ 虚拟实验室已停止');
  }

  public restart(): void {
    console.log('🔄 重启虚拟实验室...');
    this.stop();
    setTimeout(() => {
      this.start();
    }, 100);
  }

  private cleanup(): void {
    this.stop();
    
    if (this.renderer) {
      this.renderer.dispose();
    }
    
    if (this.gui) {
      this.gui.destroy();
    }
    
    if (this.stats && this.stats.dom && this.stats.dom.parentNode) {
      this.stats.dom.remove();
    }
  }

  public dispose(): void {
    this.cleanup();
    this.isInitialized = false;
    console.log('✓ 虚拟实验室已清理');
  }
}
