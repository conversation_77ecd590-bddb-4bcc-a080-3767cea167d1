import * as THREE from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js';
import { GUI } from 'lil-gui';

export class MinimalLab {
  private renderer!: THREE.WebGLRenderer;
  private scene!: THREE.Scene;
  private camera!: THREE.PerspectiveCamera;
  private controls!: OrbitControls;
  private gui!: GUI;
  private animationId: number = 0;

  constructor() {
    this.init();
  }

  private init(): void {
    console.log('🔧 初始化最小化实验室...');
    
    // 创建场景
    this.scene = new THREE.Scene();
    this.scene.background = new THREE.Color(0x1a1a1a);
    console.log('✓ 场景创建成功');

    // 创建相机
    this.camera = new THREE.PerspectiveCamera(
      75,
      window.innerWidth / window.innerHeight,
      0.1,
      1000
    );
    this.camera.position.set(0, 5, 10);
    this.camera.lookAt(0, 0, 0);
    console.log('✓ 相机创建成功');

    // 创建渲染器
    this.renderer = new THREE.WebGLRenderer({ antialias: true });
    this.renderer.setSize(window.innerWidth, window.innerHeight);
    this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
    
    const container = document.getElementById('app');
    if (container) {
      container.appendChild(this.renderer.domElement);
    }
    console.log('✓ 渲染器创建成功');

    // 创建控制器
    this.controls = new OrbitControls(this.camera, this.renderer.domElement);
    this.controls.enableDamping = true;
    this.controls.dampingFactor = 0.05;
    console.log('✓ 控制器创建成功');

    // 添加基础光照
    const ambientLight = new THREE.AmbientLight(0x404040, 0.4);
    this.scene.add(ambientLight);
    
    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(10, 10, 5);
    this.scene.add(directionalLight);
    console.log('✓ 光照设置成功');

    // 添加网格
    const gridHelper = new THREE.GridHelper(20, 20, 0x444444, 0x222222);
    gridHelper.position.y = -2;
    this.scene.add(gridHelper);
    console.log('✓ 网格添加成功');

    // 创建简单的透镜
    this.createSimpleLens();
    
    // 创建简单的光线
    this.createSimpleRays();

    // 设置GUI
    this.setupGUI();

    // 设置事件监听器
    this.setupEventListeners();

    console.log('✅ 最小化实验室初始化完成');
  }

  private createSimpleLens(): void {
    // 创建一个简单的透镜几何体
    const lensGeometry = new THREE.CylinderGeometry(1.5, 1.5, 0.3, 32);
    const lensMaterial = new THREE.MeshPhysicalMaterial({
      color: 0x88ccff,
      transparent: true,
      opacity: 0.6,
      roughness: 0.1,
      metalness: 0.0,
      transmission: 0.8
    });
    
    const lens = new THREE.Mesh(lensGeometry, lensMaterial);
    lens.position.set(0, 0, 0);
    lens.rotation.z = Math.PI / 2; // 旋转90度使其垂直
    this.scene.add(lens);
    
    console.log('✓ 简单透镜创建成功');
  }

  private createSimpleRays(): void {
    // 创建几条简单的光线
    const rayMaterial = new THREE.LineBasicMaterial({ 
      color: 0xffff00,
      transparent: true,
      opacity: 0.8
    });

    // 创建5条平行光线
    for (let i = -2; i <= 2; i++) {
      const points = [
        new THREE.Vector3(-8, i * 0.5, 0),
        new THREE.Vector3(8, i * 0.5, 0)
      ];
      
      const rayGeometry = new THREE.BufferGeometry().setFromPoints(points);
      const ray = new THREE.Line(rayGeometry, rayMaterial);
      this.scene.add(ray);
    }
    
    console.log('✓ 简单光线创建成功');
  }

  private setupGUI(): void {
    this.gui = new GUI({ title: '最小化光学实验室' });
    
    const params = {
      resetCamera: () => {
        this.camera.position.set(0, 5, 10);
        this.camera.lookAt(0, 0, 0);
        this.controls.reset();
      },
      toggleWireframe: false
    };

    this.gui.add(params, 'resetCamera').name('重置相机');
    this.gui.add(params, 'toggleWireframe').name('线框模式').onChange((value: boolean) => {
      this.scene.traverse((child) => {
        if (child instanceof THREE.Mesh && child.material instanceof THREE.Material) {
          (child.material as any).wireframe = value;
        }
      });
    });

    console.log('✓ GUI设置成功');
  }

  private setupEventListeners(): void {
    window.addEventListener('resize', () => {
      this.camera.aspect = window.innerWidth / window.innerHeight;
      this.camera.updateProjectionMatrix();
      this.renderer.setSize(window.innerWidth, window.innerHeight);
    });

    window.addEventListener('keydown', (event) => {
      switch (event.code) {
        case 'KeyR':
          this.camera.position.set(0, 5, 10);
          this.camera.lookAt(0, 0, 0);
          this.controls.reset();
          break;
        case 'KeyH':
          if (this.gui.domElement.style.display === 'none') {
            this.gui.show();
          } else {
            this.gui.hide();
          }
          break;
      }
    });

    console.log('✓ 事件监听器设置成功');
  }

  public start(): void {
    this.animate();
    console.log('✓ 动画循环启动');
  }

  private animate(): void {
    this.animationId = requestAnimationFrame(this.animate.bind(this));
    
    // 更新控制器
    this.controls.update();
    
    // 渲染场景
    this.renderer.render(this.scene, this.camera);
  }

  public stop(): void {
    if (this.animationId) {
      cancelAnimationFrame(this.animationId);
      this.animationId = 0;
    }
  }

  public dispose(): void {
    this.stop();
    this.renderer.dispose();
    this.gui.destroy();
  }
}
