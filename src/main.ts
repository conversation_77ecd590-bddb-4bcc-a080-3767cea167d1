import { SimpleTest } from './debug/SimpleTest';
import { MinimalLab } from './minimal/MinimalLab';
import { SafeVirtualLab } from './core/SafeVirtualLab';
import { HighPerformanceLab } from './core/HighPerformanceLab';
import { StatusCheck } from './status/StatusCheck';
import { ModuleTest } from './test/ModuleTest';
import './style.css';

// 运行状态检查和测试（开发模式）
if (import.meta.env.DEV) {
    console.log("I was called");
  StatusCheck.runFullCheck().then(() => {
    SimpleTest.runQuickTest();
    return ModuleTest.testModuleImports();
  }).then(() => {
    console.log('🚀 开始初始化虚拟实验室...');
    return ModuleTest.testVirtualLabCreation();
  }).then((success) => {
    if (success) {
      console.log('✅ 完整版虚拟实验室启动成功！');
      console.log('🎮 交互说明：');
      console.log('  - 鼠标拖拽：旋转视角');
      console.log('  - 鼠标滚轮：缩放');
      console.log('  - 右键菜单：添加透镜/光源');
      console.log('  - R键：重置场景');
      console.log('  - H键：显示/隐藏控制面板');
      console.log('  - 使用右侧GUI面板调节参数');
    } else {
      console.log('⚠️ 完整版启动失败，使用安全版本');
      const safeLab = new SafeVirtualLab();
      safeLab.init();
      safeLab.start();
      console.log('✅ 安全版实验室启动成功（回退模式）');
    }
  }).catch(error => {
    console.error('❌ 测试过程中发生错误:', error);
    console.log('🔄 尝试启动安全版本...');
    const safeLab = new SafeVirtualLab();
    safeLab.init();
    safeLab.start();
    console.log('✅ 安全版实验室启动成功（回退模式）');
  });
} else {
  // 生产模式直接启动
  console.log('🚀 生产模式启动...');
  import('./core/VirtualLab').then(({ VirtualLab }) => {
    const lab = new VirtualLab();
    lab.init();
    lab.start();
    console.log('✅ 虚拟实验室启动成功');
  }).catch(error => {
    console.error('❌ 启动失败:', error);
  });
}