import * as THREE from 'three';
import { LensManager } from '../optics/LensManager';
import { LightSourceManager } from '../optics/LightSourceManager';

export class DemoScenes {
  private lensManager: LensManager;
  private lightSourceManager: LightSourceManager;

  constructor(lensManager: LensManager, lightSourceManager: LightSourceManager) {
    this.lensManager = lensManager;
    this.lightSourceManager = lightSourceManager;
  }

  /**
   * 演示场景1：凸透镜会聚光线
   */
  public createConvexLensDemo(): void {
    this.clearAll();

    // 添加凸透镜
    this.lensManager.addLens({
      type: 'convex',
      position: new THREE.Vector3(0, 0, 0),
      focalLength: 5,
      diameter: 4,
      thickness: 0.8
    });

    // 添加平行光源
    this.lightSourceManager.addParallelLight({
      position: new THREE.Vector3(-10, 0, 0),
      intensity: 1.5,
      color: 0xffffff,
      rayCount: 16,
      direction: new THREE.Vector3(1, 0, 0)
    });

    console.log('演示场景：凸透镜会聚平行光');
  }

  /**
   * 演示场景2：凹透镜发散光线
   */
  public createConcaveLensDemo(): void {
    this.clearAll();

    // 添加凹透镜
    this.lensManager.addLens({
      type: 'concave',
      position: new THREE.Vector3(0, 0, 0),
      focalLength: -5,
      diameter: 4,
      thickness: 0.8
    });

    // 添加平行光源
    this.lightSourceManager.addParallelLight({
      position: new THREE.Vector3(-10, 0, 0),
      intensity: 1.5,
      color: 0xffffff,
      rayCount: 16,
      direction: new THREE.Vector3(1, 0, 0)
    });

    console.log('演示场景：凹透镜发散平行光');
  }

  /**
   * 演示场景3：点光源通过凸透镜
   */
  public createPointLightDemo(): void {
    this.clearAll();

    // 添加凸透镜
    this.lensManager.addLens({
      type: 'convex',
      position: new THREE.Vector3(0, 0, 0),
      focalLength: 6,
      diameter: 5,
      thickness: 1.0
    });

    // 添加点光源
    this.lightSourceManager.addPointLight({
      position: new THREE.Vector3(-8, 0, 0),
      intensity: 2.0,
      color: 0xffff00,
      rayCount: 20
    });

    console.log('演示场景：点光源通过凸透镜');
  }

  /**
   * 演示场景4：双透镜系统
   */
  public createDoubleLensDemo(): void {
    this.clearAll();

    // 添加第一个凸透镜
    this.lensManager.addLens({
      type: 'convex',
      position: new THREE.Vector3(-3, 0, 0),
      focalLength: 4,
      diameter: 3,
      thickness: 0.6
    });

    // 添加第二个凸透镜
    this.lensManager.addLens({
      type: 'convex',
      position: new THREE.Vector3(3, 0, 0),
      focalLength: 4,
      diameter: 3,
      thickness: 0.6
    });

    // 添加平行光源
    this.lightSourceManager.addParallelLight({
      position: new THREE.Vector3(-12, 0, 0),
      intensity: 1.8,
      color: 0x00ffff,
      rayCount: 12,
      direction: new THREE.Vector3(1, 0, 0)
    });

    console.log('演示场景：双透镜系统');
  }

  /**
   * 演示场景5：彩色光源演示
   */
  public createColorDemo(): void {
    this.clearAll();

    // 添加凸透镜
    this.lensManager.addLens({
      type: 'convex',
      position: new THREE.Vector3(0, 0, 0),
      focalLength: 5,
      diameter: 4,
      thickness: 0.8
    });

    // 添加红色点光源
    this.lightSourceManager.addPointLight({
      position: new THREE.Vector3(-8, 2, 0),
      intensity: 1.5,
      color: 0xff0000,
      rayCount: 12
    });

    // 添加绿色点光源
    this.lightSourceManager.addPointLight({
      position: new THREE.Vector3(-8, 0, 0),
      intensity: 1.5,
      color: 0x00ff00,
      rayCount: 12
    });

    // 添加蓝色点光源
    this.lightSourceManager.addPointLight({
      position: new THREE.Vector3(-8, -2, 0),
      intensity: 1.5,
      color: 0x0000ff,
      rayCount: 12
    });

    console.log('演示场景：彩色光源演示');
  }

  /**
   * 演示场景6：焦距对比
   */
  public createFocalLengthDemo(): void {
    this.clearAll();

    // 添加短焦距透镜
    this.lensManager.addLens({
      type: 'convex',
      position: new THREE.Vector3(-4, 2, 0),
      focalLength: 3,
      diameter: 3,
      thickness: 0.8
    });

    // 添加长焦距透镜
    this.lensManager.addLens({
      type: 'convex',
      position: new THREE.Vector3(-4, -2, 0),
      focalLength: 8,
      diameter: 3,
      thickness: 0.8
    });

    // 添加平行光源
    this.lightSourceManager.addParallelLight({
      position: new THREE.Vector3(-12, 0, 0),
      intensity: 1.5,
      color: 0xffffff,
      rayCount: 20,
      direction: new THREE.Vector3(1, 0, 0)
    });

    console.log('演示场景：不同焦距透镜对比');
  }

  /**
   * 演示场景7：复杂光学系统
   */
  public createComplexDemo(): void {
    this.clearAll();

    // 添加凹透镜
    this.lensManager.addLens({
      type: 'concave',
      position: new THREE.Vector3(-6, 0, 0),
      focalLength: -4,
      diameter: 4,
      thickness: 0.6
    });

    // 添加凸透镜
    this.lensManager.addLens({
      type: 'convex',
      position: new THREE.Vector3(0, 0, 0),
      focalLength: 6,
      diameter: 5,
      thickness: 1.0
    });

    // 添加第二个凸透镜
    this.lensManager.addLens({
      type: 'convex',
      position: new THREE.Vector3(8, 0, 0),
      focalLength: 4,
      diameter: 3,
      thickness: 0.8
    });

    // 添加点光源
    this.lightSourceManager.addPointLight({
      position: new THREE.Vector3(-12, 0, 0),
      intensity: 2.5,
      color: 0xffffff,
      rayCount: 24
    });

    console.log('演示场景：复杂光学系统');
  }

  /**
   * 清空所有透镜和光源
   */
  private clearAll(): void {
    this.lensManager.clear();
    this.lightSourceManager.clear();
  }

  /**
   * 获取所有可用的演示场景
   */
  public getAvailableScenes(): { name: string; description: string; action: () => void }[] {
    return [
      {
        name: '凸透镜会聚',
        description: '平行光通过凸透镜会聚到焦点',
        action: () => this.createConvexLensDemo()
      },
      {
        name: '凹透镜发散',
        description: '平行光通过凹透镜发散',
        action: () => this.createConcaveLensDemo()
      },
      {
        name: '点光源成像',
        description: '点光源通过凸透镜的光路',
        action: () => this.createPointLightDemo()
      },
      {
        name: '双透镜系统',
        description: '两个凸透镜组成的光学系统',
        action: () => this.createDoubleLensDemo()
      },
      {
        name: '彩色光源',
        description: '不同颜色的光源演示',
        action: () => this.createColorDemo()
      },
      {
        name: '焦距对比',
        description: '不同焦距透镜的对比',
        action: () => this.createFocalLengthDemo()
      },
      {
        name: '复杂系统',
        description: '凹透镜和凸透镜组合系统',
        action: () => this.createComplexDemo()
      }
    ];
  }
}
