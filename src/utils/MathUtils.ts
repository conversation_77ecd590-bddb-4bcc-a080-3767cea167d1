import * as THREE from 'three';

export class MathUtils {
  /**
   * 计算两点之间的距离
   */
  static distance(p1: THREE.Vector3, p2: THREE.Vector3): number {
    return p1.distanceTo(p2);
  }

  /**
   * 计算向量的反射
   */
  static reflect(incident: THREE.Vector3, normal: THREE.Vector3): THREE.Vector3 {
    const dot = incident.dot(normal);
    return incident.clone().sub(normal.clone().multiplyScalar(2 * dot));
  }

  /**
   * 计算折射向量
   */
  static refract(incident: THREE.Vector3, normal: THREE.Vector3, eta: number): THREE.Vector3 | null {
    const cosI = -normal.dot(incident);
    const sinT2 = eta * eta * (1.0 - cosI * cosI);
    
    if (sinT2 > 1.0) {
      return null; // 全反射
    }
    
    const cosT = Math.sqrt(1.0 - sinT2);
    return incident.clone()
      .multiplyScalar(eta)
      .add(normal.clone().multiplyScalar(eta * cosI - cosT));
  }

  /**
   * 将角度转换为弧度
   */
  static degToRad(degrees: number): number {
    return degrees * Math.PI / 180;
  }

  /**
   * 将弧度转换为角度
   */
  static radToDeg(radians: number): number {
    return radians * 180 / Math.PI;
  }

  /**
   * 限制数值在指定范围内
   */
  static clamp(value: number, min: number, max: number): number {
    return Math.max(min, Math.min(max, value));
  }

  /**
   * 线性插值
   */
  static lerp(a: number, b: number, t: number): number {
    return a + (b - a) * t;
  }

  /**
   * 计算透镜焦点位置
   */
  static calculateFocalPoint(
    lensPosition: THREE.Vector3,
    focalLength: number,
    lensNormal: THREE.Vector3
  ): THREE.Vector3 {
    return lensPosition.clone().add(lensNormal.clone().multiplyScalar(focalLength));
  }

  /**
   * 计算光线与平面的交点
   */
  static rayPlaneIntersection(
    rayOrigin: THREE.Vector3,
    rayDirection: THREE.Vector3,
    planePoint: THREE.Vector3,
    planeNormal: THREE.Vector3
  ): THREE.Vector3 | null {
    const denom = planeNormal.dot(rayDirection);
    
    if (Math.abs(denom) < 1e-6) {
      return null; // 射线与平面平行
    }
    
    const t = planePoint.clone().sub(rayOrigin).dot(planeNormal) / denom;
    
    if (t < 0) {
      return null; // 交点在射线起点后面
    }
    
    return rayOrigin.clone().add(rayDirection.clone().multiplyScalar(t));
  }

  /**
   * 计算光线与球面的交点
   */
  static raySphereIntersection(
    rayOrigin: THREE.Vector3,
    rayDirection: THREE.Vector3,
    sphereCenter: THREE.Vector3,
    sphereRadius: number
  ): { point: THREE.Vector3; normal: THREE.Vector3; distance: number } | null {
    const oc = rayOrigin.clone().sub(sphereCenter);
    const a = rayDirection.dot(rayDirection);
    const b = 2.0 * oc.dot(rayDirection);
    const c = oc.dot(oc) - sphereRadius * sphereRadius;
    
    const discriminant = b * b - 4 * a * c;
    
    if (discriminant < 0) {
      return null;
    }
    
    const t1 = (-b - Math.sqrt(discriminant)) / (2 * a);
    const t2 = (-b + Math.sqrt(discriminant)) / (2 * a);
    
    const t = t1 > 0.001 ? t1 : (t2 > 0.001 ? t2 : -1);
    
    if (t < 0) {
      return null;
    }
    
    const point = rayOrigin.clone().add(rayDirection.clone().multiplyScalar(t));
    const normal = point.clone().sub(sphereCenter).normalize();
    
    return { point, normal, distance: t };
  }

  /**
   * 生成均匀分布的球面点
   */
  static generateSpherePoints(count: number): THREE.Vector3[] {
    const points: THREE.Vector3[] = [];
    
    for (let i = 0; i < count; i++) {
      const phi = Math.acos(1 - 2 * (i + 0.5) / count);
      const theta = Math.PI * (1 + Math.sqrt(5)) * (i + 0.5);
      
      const x = Math.sin(phi) * Math.cos(theta);
      const y = Math.sin(phi) * Math.sin(theta);
      const z = Math.cos(phi);
      
      points.push(new THREE.Vector3(x, y, z));
    }
    
    return points;
  }

  /**
   * 计算菲涅尔反射系数
   */
  static fresnelReflectance(
    cosI: number,
    n1: number,
    n2: number
  ): number {
    const eta = n1 / n2;
    const sinT2 = eta * eta * (1 - cosI * cosI);
    
    if (sinT2 > 1) {
      return 1.0; // 全反射
    }
    
    const cosT = Math.sqrt(1 - sinT2);
    const rs = (n1 * cosI - n2 * cosT) / (n1 * cosI + n2 * cosT);
    const rp = (n1 * cosT - n2 * cosI) / (n1 * cosT + n2 * cosI);
    
    return 0.5 * (rs * rs + rp * rp);
  }
}
