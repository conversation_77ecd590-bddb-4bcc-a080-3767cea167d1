body {
  margin: 0;
  padding: 0;
  overflow: hidden;
  font-family: 'Arial', sans-serif;
  background: #1a1a1a;
}

#app {
  width: 100vw;
  height: 100vh;
  position: relative;
}

canvas {
  display: block;
  cursor: grab;
}

canvas:active {
  cursor: grabbing;
}

/* GUI 样式 */
.lil-gui {
  --background-color: rgba(30, 30, 30, 0.9);
  --text-color: #ffffff;
  --title-background-color: rgba(50, 50, 50, 0.9);
  --title-text-color: #ffffff;
  --widget-color: rgba(60, 60, 60, 0.9);
  --hover-color: rgba(80, 80, 80, 0.9);
  --focus-color: rgba(100, 100, 100, 0.9);
  --number-color: #00ff88;
  --string-color: #88ff00;
  z-index: 1000;
}

/* 信息面板 */
.info-panel {
  position: absolute;
  top: 10px;
  left: 10px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 15px;
  border-radius: 8px;
  font-size: 14px;
  max-width: 300px;
  z-index: 100;
}

.info-panel h3 {
  margin: 0 0 10px 0;
  color: #00ff88;
}

.info-panel p {
  margin: 5px 0;
  line-height: 1.4;
}

/* 加载动画 */
.loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 18px;
  z-index: 1000;
}

.loading::after {
  content: '';
  animation: dots 1.5s infinite;
}

@keyframes dots {
  0%, 20% { content: ''; }
  40% { content: '.'; }
  60% { content: '..'; }
  80%, 100% { content: '...'; }
}

/* 右键菜单样式 */
.context-menu {
  position: fixed;
  background: rgba(40, 40, 40, 0.95);
  border: 1px solid #666;
  border-radius: 6px;
  padding: 4px 0;
  min-width: 160px;
  z-index: 10000;
  display: none;
  font-family: 'Arial', sans-serif;
  font-size: 13px;
  color: white;
  box-shadow: 0 4px 12px rgba(0,0,0,0.4);
  backdrop-filter: blur(10px);
}

.context-menu-item {
  padding: 8px 16px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  user-select: none;
}

.context-menu-item:hover {
  background-color: rgba(100, 100, 100, 0.5);
}

.context-menu-separator {
  height: 1px;
  background: #666;
  margin: 4px 8px;
}

/* 选中对象高亮样式 */
.selected-object {
  outline: 2px solid #00ff88;
  outline-offset: 2px;
}

/* 悬停提示 */
.hover-tooltip {
  position: fixed;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  pointer-events: none;
  z-index: 1000;
  white-space: nowrap;
}
