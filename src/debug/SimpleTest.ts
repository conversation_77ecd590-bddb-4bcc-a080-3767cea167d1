import * as THREE from 'three';

export class SimpleTest {
  public static testBasicThreeJS(): void {
    console.log('🔧 测试基础Three.js功能...');
    
    try {
      // 测试场景创建
      const scene = new THREE.Scene();
      console.log('✓ Scene创建成功');
      
      // 测试相机创建
      const camera = new THREE.PerspectiveCamera(75, 1, 0.1, 1000);
      console.log('✓ Camera创建成功');
      
      // 测试渲染器创建
      const renderer = new THREE.WebGLRenderer();
      console.log('✓ Renderer创建成功');
      
      // 测试几何体创建
      const geometry = new THREE.BoxGeometry(1, 1, 1);
      const material = new THREE.MeshBasicMaterial({ color: 0x00ff00 });
      const cube = new THREE.Mesh(geometry, material);
      scene.add(cube);
      console.log('✓ 基础几何体创建成功');
      
      // 清理
      geometry.dispose();
      material.dispose();
      renderer.dispose();
      
      console.log('✅ 基础Three.js测试通过');
      
    } catch (error) {
      console.error('❌ 基础Three.js测试失败:', error);
    }
  }

  public static testModuleImports(): void {
    console.log('📦 测试模块导入...');
    
    try {
      // 这些导入在编译时已经检查过了
      console.log('✓ 所有模块导入成功');
      
    } catch (error) {
      console.error('❌ 模块导入测试失败:', error);
    }
  }

  public static runQuickTest(): void {
    console.log('🚀 运行快速测试...');
    this.testBasicThreeJS();
    this.testModuleImports();
    console.log('✅ 快速测试完成');
  }
}
