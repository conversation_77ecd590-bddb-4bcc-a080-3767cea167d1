export class StatusCheck {
  public static checkEnvironment(): void {
    console.log('🔍 环境检查开始...');
    
    // 检查浏览器支持
    this.checkBrowserSupport();
    
    // 检查依赖
    this.checkDependencies();
    
    // 检查Vite环境变量
    this.checkViteEnv();
    
    console.log('✅ 环境检查完成');
  }

  private static checkBrowserSupport(): void {
    console.log('📱 检查浏览器支持...');
    
    // 检查WebGL支持
    const canvas = document.createElement('canvas');
    const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
    
    if (gl) {
      console.log('✓ WebGL支持正常');
      console.log(`  - WebGL版本: ${gl.getParameter(gl.VERSION)}`);
      console.log(`  - 渲染器: ${gl.getParameter(gl.RENDERER)}`);
    } else {
      console.error('❌ WebGL不支持');
    }
    
    // 检查ES6支持
    try {
      eval('const test = () => {}; class Test {}');
      console.log('✓ ES6支持正常');
    } catch (e) {
      console.error('❌ ES6不支持');
    }
    
    // 检查requestAnimationFrame
    if (typeof requestAnimationFrame !== 'undefined') {
      console.log('✓ requestAnimationFrame支持正常');
    } else {
      console.error('❌ requestAnimationFrame不支持');
    }
  }

  private static checkDependencies(): void {
    console.log('📦 检查依赖...');
    
    try {
      // 检查Three.js
      import('three').then(() => {
        console.log('✓ Three.js加载成功');
      }).catch(() => {
        console.error('❌ Three.js加载失败');
      });
      
      // 检查lil-gui
      import('lil-gui').then(() => {
        console.log('✓ lil-gui加载成功');
      }).catch(() => {
        console.error('❌ lil-gui加载失败');
      });
      
      // 检查stats.js
      import('stats.js').then(() => {
        console.log('✓ stats.js加载成功');
      }).catch(() => {
        console.error('❌ stats.js加载失败');
      });
      
    } catch (error) {
      console.error('❌ 依赖检查失败:', error);
    }
  }

  private static checkViteEnv(): void {
    console.log('⚙️ 检查Vite环境...');
    
    try {
      if (typeof import.meta !== 'undefined') {
        console.log('✓ import.meta可用');
        
        if (typeof import.meta.env !== 'undefined') {
          console.log('✓ import.meta.env可用');
          console.log(`  - 开发模式: ${import.meta.env.DEV}`);
          console.log(`  - 生产模式: ${import.meta.env.PROD}`);
          console.log(`  - SSR模式: ${import.meta.env.SSR}`);
        } else {
          console.error('❌ import.meta.env不可用');
        }
      } else {
        console.error('❌ import.meta不可用');
      }
    } catch (error) {
      console.error('❌ Vite环境检查失败:', error);
    }
  }

  public static async checkModuleImports(): Promise<void> {
    console.log('🔗 检查模块导入...');

    try {
      // 检查Three.js
      await import(/* @vite-ignore */ 'three');
      console.log('✓ Three.js 导入成功');
    } catch (error) {
      console.error('❌ Three.js 导入失败:', error);
    }

    try {
      // 检查OrbitControls
      await import(/* @vite-ignore */ 'three/examples/jsm/controls/OrbitControls.js');
      console.log('✓ OrbitControls 导入成功');
    } catch (error) {
      console.error('❌ OrbitControls 导入失败:', error);
    }

    try {
      // 检查lil-gui
      await import(/* @vite-ignore */ 'lil-gui');
      console.log('✓ lil-gui 导入成功');
    } catch (error) {
      console.error('❌ lil-gui 导入失败:', error);
    }

    try {
      // 检查stats.js
      await import(/* @vite-ignore */ 'stats.js');
      console.log('✓ stats.js 导入成功');
    } catch (error) {
      console.error('❌ stats.js 导入失败:', error);
    }
  }

  public static displaySystemInfo(): void {
    console.log('💻 系统信息:');
    console.log(`  - 用户代理: ${navigator.userAgent}`);
    console.log(`  - 平台: ${navigator.platform}`);
    console.log(`  - 语言: ${navigator.language}`);
    console.log(`  - 屏幕分辨率: ${screen.width}x${screen.height}`);
    console.log(`  - 设备像素比: ${window.devicePixelRatio}`);
    console.log(`  - 视口大小: ${window.innerWidth}x${window.innerHeight}`);
  }

  public static async runFullCheck(): Promise<void> {
    console.log('🚀 开始完整状态检查...');
    console.log('='.repeat(50));

    this.displaySystemInfo();
    console.log('-'.repeat(30));

    this.checkEnvironment();
    console.log('-'.repeat(30));

    await this.checkModuleImports();
    console.log('-'.repeat(30));

    console.log('✅ 完整状态检查完成');
    console.log('='.repeat(50));
  }
}
