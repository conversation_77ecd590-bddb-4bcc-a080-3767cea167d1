import { G<PERSON> } from 'lil-gui';
import * as THREE from 'three';
import { LensManager } from '../optics/LensManager';
import { LightSourceManager } from '../optics/LightSourceManager';
import { RayTracer } from '../physics/RayTracer';
import { DemoScenes } from '../demos/DemoScenes';

interface UIControllerDependencies {
  lensManager: LensManager;
  lightSourceManager: LightSourceManager;
  rayTracer: RayTracer;
  scene: THREE.Scene;
}

export class UIController {
  private gui!: GUI;
  private dependencies!: UIControllerDependencies;
  private demoScenes!: DemoScenes;
  
  // 控制参数
  private params = {
    // 透镜控制
    lensType: 'convex' as 'convex' | 'concave',
    lensFocalLength: 5,
    lensDiameter: 3,
    lensThickness: 0.5,
    lensPositionX: 0,
    lensPositionY: 0,
    lensPositionZ: 0,
    
    // 光源控制
    lightType: 'point' as 'point' | 'parallel',
    lightIntensity: 1.0,
    lightColor: '#ffffff',
    lightPositionX: -8,
    lightPositionY: 0,
    lightPositionZ: 0,
    lightDirectionX: 1,
    lightDirectionY: 0,
    lightDirectionZ: 0,
    rayCount: 12,
    
    // 渲染控制
    showRays: true,
    maxRayLength: 50,
    maxReflections: 5,
    
    // 场景控制
    showGrid: true,
    showAxes: true,
    
    // 操作
    addLens: () => this.addLens(),
    addLight: () => this.addLight(),
    clearScene: () => this.clearScene(),
    resetCamera: () => this.resetCamera(),

    // 选择模式
    selectionMode: false,

    // 对象计数
    lensCount: 0,
    lightCount: 0
  };

  public init(gui: GUI, dependencies: UIControllerDependencies): void {
    this.gui = gui;
    this.dependencies = dependencies;
    this.demoScenes = new DemoScenes(dependencies.lensManager, dependencies.lightSourceManager);
    this.setupGUI();
    this.updateRayTracerSettings();
  }

  private setupGUI(): void {
    // 透镜控制面板
    const lensFolder = this.gui.addFolder('透镜控制');
    lensFolder.add(this.params, 'lensType', ['convex', 'concave']).name('透镜类型');
    lensFolder.add(this.params, 'lensFocalLength', 1, 20, 0.1).name('焦距');
    lensFolder.add(this.params, 'lensDiameter', 1, 10, 0.1).name('直径');
    lensFolder.add(this.params, 'lensThickness', 0.1, 2, 0.1).name('厚度');
    
    const lensPositionFolder = lensFolder.addFolder('透镜位置');
    lensPositionFolder.add(this.params, 'lensPositionX', -20, 20, 0.1).name('X');
    lensPositionFolder.add(this.params, 'lensPositionY', -10, 10, 0.1).name('Y');
    lensPositionFolder.add(this.params, 'lensPositionZ', -20, 20, 0.1).name('Z');
    
    lensFolder.add(this.params, 'addLens').name('添加透镜');
    lensFolder.open();

    // 光源控制面板
    const lightFolder = this.gui.addFolder('光源控制');
    lightFolder.add(this.params, 'lightType', ['point', 'parallel']).name('光源类型');
    lightFolder.add(this.params, 'lightIntensity', 0, 5, 0.1).name('强度');
    lightFolder.addColor(this.params, 'lightColor').name('颜色');
    lightFolder.add(this.params, 'rayCount', 4, 36, 1).name('射线数量');
    
    const lightPositionFolder = lightFolder.addFolder('光源位置');
    lightPositionFolder.add(this.params, 'lightPositionX', -20, 20, 0.1).name('X');
    lightPositionFolder.add(this.params, 'lightPositionY', -10, 10, 0.1).name('Y');
    lightPositionFolder.add(this.params, 'lightPositionZ', -20, 20, 0.1).name('Z');
    
    const lightDirectionFolder = lightFolder.addFolder('平行光方向');
    lightDirectionFolder.add(this.params, 'lightDirectionX', -1, 1, 0.1).name('X');
    lightDirectionFolder.add(this.params, 'lightDirectionY', -1, 1, 0.1).name('Y');
    lightDirectionFolder.add(this.params, 'lightDirectionZ', -1, 1, 0.1).name('Z');
    
    lightFolder.add(this.params, 'addLight').name('添加光源');
    lightFolder.open();

    // 渲染控制面板
    const renderFolder = this.gui.addFolder('渲染控制');
    renderFolder.add(this.params, 'showRays').name('显示光线').onChange(() => {
      this.updateRayVisibility();
    });
    renderFolder.add(this.params, 'maxRayLength', 10, 100, 1).name('最大光线长度').onChange(() => {
      this.updateRayTracerSettings();
    });
    renderFolder.add(this.params, 'maxReflections', 1, 10, 1).name('最大反射次数').onChange(() => {
      this.updateRayTracerSettings();
    });

    // 场景控制面板
    const sceneFolder = this.gui.addFolder('场景控制');
    sceneFolder.add(this.params, 'showGrid').name('显示网格');
    sceneFolder.add(this.params, 'showAxes').name('显示坐标轴');
    
    // 演示场景面板
    const demoFolder = this.gui.addFolder('演示场景');
    const scenes = this.demoScenes.getAvailableScenes();
    scenes.forEach(scene => {
      demoFolder.add({ action: scene.action }, 'action').name(scene.name);
    });
    demoFolder.open();

    // 对象管理面板
    const objectFolder = this.gui.addFolder('对象管理');
    objectFolder.add(this.params, 'lensCount').name('透镜数量').listen();
    objectFolder.add(this.params, 'lightCount').name('光源数量').listen();
    objectFolder.add(this.params, 'selectionMode').name('选择模式');

    // 操作面板
    const actionFolder = this.gui.addFolder('操作');
    actionFolder.add(this.params, 'clearScene').name('清空场景');
    actionFolder.add(this.params, 'resetCamera').name('重置相机');
    actionFolder.open();

    // 添加信息面板
    this.createInfoPanel();
  }

  private addLens(): void {
    const position = new THREE.Vector3(
      this.params.lensPositionX,
      this.params.lensPositionY,
      this.params.lensPositionZ
    );

    this.dependencies.lensManager.addLens({
      type: this.params.lensType,
      position: position,
      focalLength: this.params.lensFocalLength,
      diameter: this.params.lensDiameter,
      thickness: this.params.lensThickness
    });

    console.log(`添加${this.params.lensType === 'convex' ? '凸' : '凹'}透镜`);
  }

  private addLight(): void {
    const position = new THREE.Vector3(
      this.params.lightPositionX,
      this.params.lightPositionY,
      this.params.lightPositionZ
    );

    const color = parseInt(this.params.lightColor.replace('#', ''), 16);

    if (this.params.lightType === 'point') {
      this.dependencies.lightSourceManager.addPointLight({
        position: position,
        intensity: this.params.lightIntensity,
        color: color,
        rayCount: this.params.rayCount
      });
    } else {
      const direction = new THREE.Vector3(
        this.params.lightDirectionX,
        this.params.lightDirectionY,
        this.params.lightDirectionZ
      ).normalize();

      this.dependencies.lightSourceManager.addParallelLight({
        position: position,
        intensity: this.params.lightIntensity,
        color: color,
        rayCount: this.params.rayCount,
        direction: direction
      });
    }

    console.log(`添加${this.params.lightType === 'point' ? '点' : '平行'}光源`);
  }

  private clearScene(): void {
    this.dependencies.lensManager.clear();
    this.dependencies.lightSourceManager.clear();
    this.dependencies.rayTracer.clearRays(this.dependencies.scene);
    this.updateObjectCounts();
    console.log('场景已清空');
  }

  private resetCamera(): void {
    // 这个方法需要在VirtualLab中实现
    console.log('重置相机位置');
  }

  private updateRayVisibility(): void {
    // 实现光线显示/隐藏逻辑
    const rayGroup = this.dependencies.scene.getObjectByName('RayGroup');
    if (rayGroup) {
      rayGroup.visible = this.params.showRays;
    }
  }

  private updateRayTracerSettings(): void {
    this.dependencies.rayTracer.setMaxRayLength(this.params.maxRayLength);
    this.dependencies.rayTracer.setMaxReflections(this.params.maxReflections);
  }

  private createInfoPanel(): void {
    const infoDiv = document.createElement('div');
    infoDiv.className = 'info-panel';
    infoDiv.innerHTML = `
      <h3>虚拟光学实验室</h3>
      <p><strong>交互操作：</strong></p>
      <p>• 鼠标拖拽：旋转视角</p>
      <p>• 鼠标滚轮：缩放</p>
      <p>• 右键点击：添加透镜/光源</p>
      <p>• 拖拽对象：移动位置</p>
      <p>• 双击对象：查看属性</p>
      <p>• Delete键：删除选中对象</p>
      <p><strong>快捷键：</strong></p>
      <p>• R键：重置场景</p>
      <p>• H键：显示/隐藏控制面板</p>
      <p>• Esc键：取消选择</p>
      <p><strong>功能：</strong></p>
      <p>• 支持凸透镜和凹透镜</p>
      <p>• 支持点光源和平行光</p>
      <p>• 实时光线追踪</p>
      <p>• 可调节透镜焦距</p>
      <p>• 交互式对象操作</p>
    `;
    document.body.appendChild(infoDiv);
  }

  private updateObjectCounts(): void {
    this.params.lensCount = this.dependencies.lensManager.getLensCount();
    this.params.lightCount = this.dependencies.lightSourceManager.getLightSourceCount();
  }

  public refreshCounts(): void {
    this.updateObjectCounts();
  }

  public updateLensCount(count: number): void {
    // 更新透镜数量显示
  }

  public updateLightSourceCount(count: number): void {
    // 更新光源数量显示
  }
}
