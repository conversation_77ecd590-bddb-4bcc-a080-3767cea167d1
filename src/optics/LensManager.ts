import * as THREE from 'three';
import { Lens, type LensConfig } from './Lens';

export class LensManager {
  private scene: THREE.Scene;
  private lenses: Lens[] = [];
  private lensGroup: THREE.Group;

  constructor(scene: THREE.Scene) {
    this.scene = scene;
    this.lensGroup = new THREE.Group();
    this.lensGroup.name = 'LensGroup';
    this.scene.add(this.lensGroup);
  }

  public addLens(config: LensConfig): Lens {
    const lens = new Lens(config);
    this.lenses.push(lens);
    this.lensGroup.add(lens.getMesh());
    
    console.log(`添加${config.type === 'convex' ? '凸' : '凹'}透镜，焦距: ${config.focalLength}`);
    return lens;
  }

  public removeLens(lens: Lens): void {
    const index = this.lenses.indexOf(lens);
    if (index > -1) {
      this.lenses.splice(index, 1);
      this.lensGroup.remove(lens.getMesh());
      lens.dispose();
    }
  }

  public getLenses(): Lens[] {
    return [...this.lenses];
  }

  public getLensByIndex(index: number): Lens | null {
    return this.lenses[index] || null;
  }

  public updateLens(index: number, config: Partial<LensConfig>): void {
    const lens = this.lenses[index];
    if (lens) {
      lens.updateConfig(config);
    }
  }

  public moveLens(index: number, position: THREE.Vector3): void {
    const lens = this.lenses[index];
    if (lens) {
      lens.setPosition(position);
    }
  }

  public rotateLens(index: number, rotation: THREE.Euler): void {
    const lens = this.lenses[index];
    if (lens) {
      lens.setRotation(rotation);
    }
  }

  public clear(): void {
    this.lenses.forEach(lens => {
      this.lensGroup.remove(lens.getMesh());
      lens.dispose();
    });
    this.lenses = [];
  }

  public setLensVisibility(index: number, visible: boolean): void {
    const lens = this.lenses[index];
    if (lens) {
      lens.setVisible(visible);
    }
  }

  public getAllLensesData(): any[] {
    return this.lenses.map((lens, index) => ({
      index,
      type: lens.getType(),
      position: lens.getPosition(),
      focalLength: lens.getFocalLength(),
      diameter: lens.getDiameter(),
      thickness: lens.getThickness()
    }));
  }

  public getLensCount(): number {
    return this.lenses.length;
  }

  public dispose(): void {
    this.clear();
    this.scene.remove(this.lensGroup);
  }
}
