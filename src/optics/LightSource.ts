import * as THREE from 'three';

export interface LightSourceConfig {
  position: THREE.Vector3;
  intensity: number;
  color: number;
  rayCount?: number;
}

export abstract class BaseLightSource {
  protected config: LightSourceConfig;
  protected group: THREE.Group;
  protected mesh!: THREE.Mesh;
  protected light!: THREE.Light;

  constructor(config: LightSourceConfig) {
    this.config = {
      rayCount: 12,
      ...config
    };
    this.group = new THREE.Group();
    this.createLightSource();
  }

  protected abstract createLightSource(): void;
  public abstract getType(): string;
  public abstract generateRayDirections(): THREE.Vector3[];

  protected createBaseMesh(geometry: THREE.BufferGeometry, color: number): void {
    const material = new THREE.MeshBasicMaterial({
      color,
      transparent: true,
      opacity: 0.8
    });
    this.mesh = new THREE.Mesh(geometry, material);
    this.mesh.position.copy(this.config.position);
    this.mesh.userData = {
      type: 'lightSource',
      selectable: true
    };
    this.group.add(this.mesh);

    // 设置group的userData
    this.group.userData = {
      type: 'lightSource',
      selectable: true
    };
  }

  public updateConfig(newConfig: Partial<LightSourceConfig>): void {
    this.config = { ...this.config, ...newConfig };
    
    if (newConfig.position) {
      this.setPosition(newConfig.position);
    }
    if (newConfig.intensity !== undefined) {
      this.setIntensity(newConfig.intensity);
    }
    if (newConfig.color !== undefined) {
      this.setColor(newConfig.color);
    }
  }

  public setPosition(position: THREE.Vector3): void {
    this.config.position = position.clone();
    this.group.position.copy(position);
  }

  public setIntensity(intensity: number): void {
    this.config.intensity = intensity;
    this.light.intensity = intensity;
  }

  public setColor(color: number): void {
    this.config.color = color;
    this.light.color.setHex(color);
    if (this.mesh.material instanceof THREE.MeshBasicMaterial) {
      this.mesh.material.color.setHex(color);
    }
  }

  public setVisible(visible: boolean): void {
    this.group.visible = visible;
  }

  public getGroup(): THREE.Group {
    return this.group;
  }

  public getPosition(): THREE.Vector3 {
    return this.config.position.clone();
  }

  public getIntensity(): number {
    return this.config.intensity;
  }

  public getColor(): number {
    return this.config.color;
  }

  public getRayCount(): number {
    return this.config.rayCount || 12;
  }

  public dispose(): void {
    if (this.mesh.material instanceof THREE.Material) {
      this.mesh.material.dispose();
    }
    this.mesh.geometry.dispose();
  }
}

export class PointLightSource extends BaseLightSource {
  protected createLightSource(): void {
    // 创建点光源可视化
    const geometry = new THREE.SphereGeometry(0.2, 16, 8);
    this.createBaseMesh(geometry, this.config.color);
    
    // 创建Three.js点光源
    this.light = new THREE.PointLight(
      this.config.color,
      this.config.intensity,
      100
    );
    this.light.position.copy(this.config.position);
    this.group.add(this.light);
    
    // 添加光源辅助器
    const helper = new THREE.PointLightHelper(this.light as THREE.PointLight, 0.5);
    this.group.add(helper);
  }

  public getType(): string {
    return 'point';
  }

  public generateRayDirections(): THREE.Vector3[] {
    const directions: THREE.Vector3[] = [];
    const rayCount = this.getRayCount();
    
    // 生成均匀分布的射线方向
    for (let i = 0; i < rayCount; i++) {
      const phi = Math.acos(1 - 2 * (i + 0.5) / rayCount);
      const theta = Math.PI * (1 + Math.sqrt(5)) * (i + 0.5);
      
      const x = Math.sin(phi) * Math.cos(theta);
      const y = Math.sin(phi) * Math.sin(theta);
      const z = Math.cos(phi);
      
      directions.push(new THREE.Vector3(x, y, z).normalize());
    }
    
    return directions;
  }
}

export class ParallelLightSource extends BaseLightSource {
  private direction: THREE.Vector3;

  constructor(config: LightSourceConfig & { direction: THREE.Vector3 }) {
    super(config);
    this.direction = config.direction.clone().normalize();
  }

  protected createLightSource(): void {
    // 创建平行光源可视化（箭头形状）
    const geometry = new THREE.ConeGeometry(0.3, 1, 8);
    this.createBaseMesh(geometry, this.config.color);
    
    // 旋转mesh以指向光线方向
    this.mesh.lookAt(this.config.position.clone().add(this.direction));
    
    // 创建Three.js方向光
    this.light = new THREE.DirectionalLight(
      this.config.color,
      this.config.intensity
    );
    this.light.position.copy(this.config.position);
    this.light.target.position.copy(this.config.position.clone().add(this.direction));
    this.group.add(this.light);
    this.group.add(this.light.target);
    
    // 添加光源辅助器
    const helper = new THREE.DirectionalLightHelper(this.light as THREE.DirectionalLight, 1);
    this.group.add(helper);
  }

  public getType(): string {
    return 'parallel';
  }

  public generateRayDirections(): THREE.Vector3[] {
    const directions: THREE.Vector3[] = [];
    const rayCount = this.getRayCount();
    
    // 平行光的所有射线方向都相同
    for (let i = 0; i < rayCount; i++) {
      directions.push(this.direction.clone());
    }
    
    return directions;
  }

  public setDirection(direction: THREE.Vector3): void {
    this.direction = direction.clone().normalize();
    this.mesh.lookAt(this.config.position.clone().add(this.direction));
    
    if (this.light instanceof THREE.DirectionalLight) {
      this.light.target.position.copy(this.config.position.clone().add(this.direction));
    }
  }

  public getDirection(): THREE.Vector3 {
    return this.direction.clone();
  }

  public generateRayPositions(): THREE.Vector3[] {
    const positions: THREE.Vector3[] = [];
    const rayCount = this.getRayCount();
    const spacing = 0.5;
    
    // 创建垂直于光线方向的平面上的点
    const up = new THREE.Vector3(0, 1, 0);
    const right = new THREE.Vector3().crossVectors(this.direction, up).normalize();
    const actualUp = new THREE.Vector3().crossVectors(right, this.direction).normalize();
    
    const gridSize = Math.ceil(Math.sqrt(rayCount));
    const offset = (gridSize - 1) * spacing / 2;
    
    for (let i = 0; i < gridSize; i++) {
      for (let j = 0; j < gridSize && positions.length < rayCount; j++) {
        const x = (i * spacing - offset);
        const y = (j * spacing - offset);
        
        const position = this.config.position.clone()
          .add(right.clone().multiplyScalar(x))
          .add(actualUp.clone().multiplyScalar(y));
        
        positions.push(position);
      }
    }
    
    return positions;
  }
}
