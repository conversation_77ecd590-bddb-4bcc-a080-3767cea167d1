import * as THREE from 'three';
import { PointLightSource, ParallelLightSource, type LightSourceConfig } from './LightSource';

export class LightSourceManager {
  private scene: THREE.Scene;
  private lightSources: (PointLightSource | ParallelLightSource)[] = [];
  private lightGroup: THREE.Group;

  constructor(scene: THREE.Scene) {
    this.scene = scene;
    this.lightGroup = new THREE.Group();
    this.lightGroup.name = 'LightGroup';
    this.scene.add(this.lightGroup);
  }

  public addPointLight(config: LightSourceConfig): PointLightSource {
    const lightSource = new PointLightSource(config);
    this.lightSources.push(lightSource);
    this.lightGroup.add(lightSource.getGroup());
    
    console.log(`添加点光源，位置: (${config.position.x}, ${config.position.y}, ${config.position.z})`);
    return lightSource;
  }

  public addParallelLight(config: LightSourceConfig & { direction: THREE.Vector3 }): ParallelLightSource {
    const lightSource = new ParallelLightSource(config);
    this.lightSources.push(lightSource);
    this.lightGroup.add(lightSource.getGroup());
    
    console.log(`添加平行光源，方向: (${config.direction.x}, ${config.direction.y}, ${config.direction.z})`);
    return lightSource;
  }

  public removeLightSource(lightSource: PointLightSource | ParallelLightSource): void {
    const index = this.lightSources.indexOf(lightSource);
    if (index > -1) {
      this.lightSources.splice(index, 1);
      this.lightGroup.remove(lightSource.getGroup());
      lightSource.dispose();
    }
  }

  public getLightSources(): (PointLightSource | ParallelLightSource)[] {
    return [...this.lightSources];
  }

  public getLightSourceByIndex(index: number): PointLightSource | ParallelLightSource | null {
    return this.lightSources[index] || null;
  }

  public updateLightSource(index: number, config: Partial<LightSourceConfig>): void {
    const lightSource = this.lightSources[index];
    if (lightSource) {
      lightSource.updateConfig(config);
    }
  }

  public moveLightSource(index: number, position: THREE.Vector3): void {
    const lightSource = this.lightSources[index];
    if (lightSource) {
      lightSource.setPosition(position);
    }
  }

  public setLightSourceIntensity(index: number, intensity: number): void {
    const lightSource = this.lightSources[index];
    if (lightSource) {
      lightSource.setIntensity(intensity);
    }
  }

  public setLightSourceColor(index: number, color: number): void {
    const lightSource = this.lightSources[index];
    if (lightSource) {
      lightSource.setColor(color);
    }
  }

  public setLightSourceVisibility(index: number, visible: boolean): void {
    const lightSource = this.lightSources[index];
    if (lightSource) {
      lightSource.setVisible(visible);
    }
  }

  public clear(): void {
    this.lightSources.forEach(lightSource => {
      this.lightGroup.remove(lightSource.getGroup());
      lightSource.dispose();
    });
    this.lightSources = [];
  }

  public getAllLightSourcesData(): any[] {
    return this.lightSources.map((lightSource, index) => ({
      index,
      type: lightSource.getType(),
      position: lightSource.getPosition(),
      intensity: lightSource.getIntensity(),
      color: lightSource.getColor(),
      ...(lightSource instanceof ParallelLightSource && {
        direction: lightSource.getDirection()
      })
    }));
  }

  public getLightSourceCount(): number {
    return this.lightSources.length;
  }

  public dispose(): void {
    this.clear();
    this.scene.remove(this.lightGroup);
  }
}
