import * as THREE from 'three';

// 透镜配置接口
export interface LensConfig {
  type: 'convex' | 'concave';
  position: THREE.Vector3;
  focalLength: number;
  diameter: number;
  thickness: number;
  refractiveIndex?: number;
}

export class Lens {
  private config: LensConfig;
  private mesh!: THREE.Mesh;
  private geometry!: THREE.BufferGeometry;
  private material!: THREE.Material;
  private wireframe!: THREE.LineSegments;

  constructor(config: LensConfig) {
    this.config = {
      refractiveIndex: 1.5,
      ...config
    };
    this.createLens();
  }

  private createLens(): void {
    this.createGeometry();
    this.createMaterial();
    this.createMesh();
    this.createWireframe();
    this.updatePosition();
  }

  private createGeometry(): void {
    const { type, diameter, thickness } = this.config;
    const radius = diameter / 2;
    
    if (type === 'convex') {
      this.geometry = this.createConvexGeometry(radius, thickness);
    } else {
      this.geometry = this.createConcaveGeometry(radius, thickness);
    }
  }

  private createConvexGeometry(radius: number, thickness: number): THREE.BufferGeometry {
    // 创建凸透镜几何体
    const geometry = new THREE.SphereGeometry(radius, 32, 16);
    
    // 修改几何体以创建透镜形状
    const positions = geometry.attributes.position.array as Float32Array;
    
    for (let i = 0; i < positions.length; i += 3) {
      const x = positions[i];
      const y = positions[i + 1];
      const z = positions[i + 2];
      
      // 计算到中心的距离
      const distance = Math.sqrt(y * y + z * z);
      
      if (distance <= radius) {
        // 前表面曲率
        const frontCurvature = Math.sqrt(Math.max(0, radius * radius - distance * distance));
        // 后表面曲率
        const backCurvature = -Math.sqrt(Math.max(0, radius * radius - distance * distance));
        
        if (x > 0) {
          positions[i] = Math.min(x, frontCurvature * thickness / radius);
        } else {
          positions[i] = Math.max(x, backCurvature * thickness / radius);
        }
      }
    }
    
    geometry.attributes.position.needsUpdate = true;
    geometry.computeVertexNormals();
    
    return geometry;
  }

  private createConcaveGeometry(radius: number, thickness: number): THREE.BufferGeometry {
    // 创建凹透镜几何体
    const geometry = new THREE.CylinderGeometry(radius, radius, thickness, 32);
    
    const positions = geometry.attributes.position.array as Float32Array;
    
    for (let i = 0; i < positions.length; i += 3) {
      const x = positions[i];
      const y = positions[i + 1];
      const z = positions[i + 2];
      
      const distance = Math.sqrt(x * x + z * z);
      
      if (distance <= radius) {
        // 凹透镜的凹陷效果
        const depression = Math.sqrt(Math.max(0, radius * radius - distance * distance));
        
        if (y > 0) {
          positions[i + 1] = thickness / 2 - depression * thickness / (2 * radius);
        } else {
          positions[i + 1] = -thickness / 2 + depression * thickness / (2 * radius);
        }
      }
    }
    
    geometry.attributes.position.needsUpdate = true;
    geometry.computeVertexNormals();
    
    return geometry;
  }

  private createMaterial(): void {
    this.material = new THREE.MeshPhysicalMaterial({
      color: 0x88ccff,
      transparent: true,
      opacity: 0.6,
      roughness: 0.1,
      metalness: 0.0,
      clearcoat: 1.0,
      clearcoatRoughness: 0.1,
      transmission: 0.8,
      ior: this.config.refractiveIndex || 1.5,
      thickness: this.config.thickness
    });
  }

  private createMesh(): void {
    this.mesh = new THREE.Mesh(this.geometry, this.material);
    this.mesh.castShadow = true;
    this.mesh.receiveShadow = true;
    this.mesh.userData = {
      type: 'lens',
      lensType: this.config.type,
      focalLength: this.config.focalLength,
      selectable: true
    };
  }

  private createWireframe(): void {
    const wireframeGeometry = new THREE.EdgesGeometry(this.geometry);
    const wireframeMaterial = new THREE.LineBasicMaterial({ 
      color: 0xffffff,
      transparent: true,
      opacity: 0.3
    });
    this.wireframe = new THREE.LineSegments(wireframeGeometry, wireframeMaterial);
    this.mesh.add(this.wireframe);
  }

  private updatePosition(): void {
    this.mesh.position.copy(this.config.position);
  }

  public updateConfig(newConfig: Partial<LensConfig>): void {
    this.config = { ...this.config, ...newConfig };
    
    // 重新创建几何体和材质
    this.geometry.dispose();
    if (this.material instanceof THREE.Material) {
      this.material.dispose();
    }
    
    this.createGeometry();
    this.createMaterial();
    this.mesh.geometry = this.geometry;
    this.mesh.material = this.material;
    
    // 更新wireframe
    this.wireframe.geometry.dispose();
    this.wireframe.geometry = new THREE.EdgesGeometry(this.geometry);
    
    this.updatePosition();
    
    // 更新userData
    this.mesh.userData.focalLength = this.config.focalLength;
  }

  public setPosition(position: THREE.Vector3): void {
    this.config.position = position.clone();
    this.updatePosition();
  }

  public setRotation(rotation: THREE.Euler): void {
    this.mesh.rotation.copy(rotation);
  }

  public setVisible(visible: boolean): void {
    this.mesh.visible = visible;
  }

  public getMesh(): THREE.Mesh {
    return this.mesh;
  }

  public getType(): 'convex' | 'concave' {
    return this.config.type;
  }

  public getPosition(): THREE.Vector3 {
    return this.config.position.clone();
  }

  public getFocalLength(): number {
    return this.config.focalLength;
  }

  public getDiameter(): number {
    return this.config.diameter;
  }

  public getThickness(): number {
    return this.config.thickness;
  }

  public getRefractiveIndex(): number {
    return this.config.refractiveIndex || 1.5;
  }

  public dispose(): void {
    this.geometry.dispose();
    if (this.material instanceof THREE.Material) {
      this.material.dispose();
    }
    this.wireframe.geometry.dispose();
    if (this.wireframe.material instanceof THREE.Material) {
      this.wireframe.material.dispose();
    }
  }
}
