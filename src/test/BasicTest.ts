import * as THREE from 'three';
import { Lens } from '../optics/Lens';
import { PointLightSource, ParallelLightSource } from '../optics/LightSource';
import { RayTracer } from '../physics/RayTracer';
import { MathUtils } from '../utils/MathUtils';

export class BasicTest {
  public static runAllTests(): void {
    console.log('🧪 开始运行基础测试...');
    
    this.testLensCreation();
    this.testLightSourceCreation();
    this.testRayTracing();
    this.testMathUtils();
    
    console.log('✅ 所有基础测试完成！');
  }

  private static testLensCreation(): void {
    console.log('📋 测试透镜创建...');
    
    try {
      // 测试凸透镜
      const convexLens = new Lens({
        type: 'convex',
        position: new THREE.Vector3(0, 0, 0),
        focalLength: 5,
        diameter: 3,
        thickness: 0.5
      });
      
      console.log('✓ 凸透镜创建成功');
      console.log(`  - 类型: ${convexLens.getType()}`);
      console.log(`  - 焦距: ${convexLens.getFocalLength()}`);
      console.log(`  - 直径: ${convexLens.getDiameter()}`);
      
      // 测试凹透镜
      const concaveLens = new Lens({
        type: 'concave',
        position: new THREE.Vector3(2, 0, 0),
        focalLength: -3,
        diameter: 2.5,
        thickness: 0.3
      });
      
      console.log('✓ 凹透镜创建成功');
      console.log(`  - 类型: ${concaveLens.getType()}`);
      console.log(`  - 焦距: ${concaveLens.getFocalLength()}`);
      
      // 清理资源
      convexLens.dispose();
      concaveLens.dispose();
      
    } catch (error) {
      console.error('❌ 透镜创建测试失败:', error);
    }
  }

  private static testLightSourceCreation(): void {
    console.log('📋 测试光源创建...');
    
    try {
      // 测试点光源
      const pointLight = new PointLightSource({
        position: new THREE.Vector3(-5, 0, 0),
        intensity: 1.0,
        color: 0xffffff,
        rayCount: 12
      });
      
      console.log('✓ 点光源创建成功');
      console.log(`  - 类型: ${pointLight.getType()}`);
      console.log(`  - 强度: ${pointLight.getIntensity()}`);
      console.log(`  - 射线数量: ${pointLight.getRayCount()}`);
      
      // 测试平行光源
      const parallelLight = new ParallelLightSource({
        position: new THREE.Vector3(-8, 0, 0),
        intensity: 1.5,
        color: 0x00ff00,
        rayCount: 16,
        direction: new THREE.Vector3(1, 0, 0)
      });
      
      console.log('✓ 平行光源创建成功');
      console.log(`  - 类型: ${parallelLight.getType()}`);
      console.log(`  - 方向: (${parallelLight.getDirection().x}, ${parallelLight.getDirection().y}, ${parallelLight.getDirection().z})`);
      
      // 清理资源
      pointLight.dispose();
      parallelLight.dispose();
      
    } catch (error) {
      console.error('❌ 光源创建测试失败:', error);
    }
  }

  private static testRayTracing(): void {
    console.log('📋 测试光线追踪...');
    
    try {
      const rayTracer = new RayTracer();
      
      // 创建测试透镜
      const lens = new Lens({
        type: 'convex',
        position: new THREE.Vector3(0, 0, 0),
        focalLength: 5,
        diameter: 3,
        thickness: 0.5
      });
      
      // 创建测试光源
      const lightSource = new PointLightSource({
        position: new THREE.Vector3(-8, 0, 0),
        intensity: 1.0,
        color: 0xffffff,
        rayCount: 8
      });
      
      // 生成光线
      const raySegments = rayTracer.generateRays(lightSource, [lens]);
      
      console.log('✓ 光线追踪测试成功');
      console.log(`  - 生成光线段数量: ${raySegments.length}`);
      
      if (raySegments.length > 0) {
        const firstSegment = raySegments[0];
        console.log(`  - 第一段光线起点: (${firstSegment.start.x.toFixed(2)}, ${firstSegment.start.y.toFixed(2)}, ${firstSegment.start.z.toFixed(2)})`);
        console.log(`  - 第一段光线终点: (${firstSegment.end.x.toFixed(2)}, ${firstSegment.end.y.toFixed(2)}, ${firstSegment.end.z.toFixed(2)})`);
      }
      
      // 清理资源
      lens.dispose();
      lightSource.dispose();
      
    } catch (error) {
      console.error('❌ 光线追踪测试失败:', error);
    }
  }

  private static testMathUtils(): void {
    console.log('📋 测试数学工具...');
    
    try {
      // 测试距离计算
      const p1 = new THREE.Vector3(0, 0, 0);
      const p2 = new THREE.Vector3(3, 4, 0);
      const distance = MathUtils.distance(p1, p2);
      console.log(`✓ 距离计算: ${distance} (期望: 5)`);
      
      // 测试角度转换
      const degrees = 90;
      const radians = MathUtils.degToRad(degrees);
      const backToDegrees = MathUtils.radToDeg(radians);
      console.log(`✓ 角度转换: ${degrees}° → ${radians.toFixed(4)} rad → ${backToDegrees}°`);
      
      // 测试限制函数
      const clampedValue = MathUtils.clamp(15, 0, 10);
      console.log(`✓ 数值限制: clamp(15, 0, 10) = ${clampedValue} (期望: 10)`);
      
      // 测试线性插值
      const lerpValue = MathUtils.lerp(0, 10, 0.5);
      console.log(`✓ 线性插值: lerp(0, 10, 0.5) = ${lerpValue} (期望: 5)`);
      
      // 测试反射计算
      const incident = new THREE.Vector3(1, -1, 0).normalize();
      const normal = new THREE.Vector3(0, 1, 0);
      const reflected = MathUtils.reflect(incident, normal);
      console.log(`✓ 反射计算: (${incident.x.toFixed(2)}, ${incident.y.toFixed(2)}, ${incident.z.toFixed(2)}) → (${reflected.x.toFixed(2)}, ${reflected.y.toFixed(2)}, ${reflected.z.toFixed(2)})`);
      
      // 测试折射计算
      const refracted = MathUtils.refract(incident, normal, 0.75);
      if (refracted) {
        console.log(`✓ 折射计算: (${refracted.x.toFixed(2)}, ${refracted.y.toFixed(2)}, ${refracted.z.toFixed(2)})`);
      } else {
        console.log('✓ 折射计算: 全反射');
      }
      
    } catch (error) {
      console.error('❌ 数学工具测试失败:', error);
    }
  }

  public static testPerformance(): void {
    console.log('🚀 开始性能测试...');
    
    const startTime = performance.now();
    
    // 创建大量透镜和光源进行性能测试
    const lenses: Lens[] = [];
    const lightSources: PointLightSource[] = [];
    
    // 创建10个透镜
    for (let i = 0; i < 10; i++) {
      lenses.push(new Lens({
        type: i % 2 === 0 ? 'convex' : 'concave',
        position: new THREE.Vector3(i * 2, 0, 0),
        focalLength: 3 + i,
        diameter: 2 + i * 0.5,
        thickness: 0.5
      }));
    }
    
    // 创建5个光源
    for (let i = 0; i < 5; i++) {
      lightSources.push(new PointLightSource({
        position: new THREE.Vector3(-10, i * 2, 0),
        intensity: 1.0,
        color: 0xffffff,
        rayCount: 20
      }));
    }
    
    const rayTracer = new RayTracer();
    let totalRaySegments = 0;
    
    // 对每个光源进行光线追踪
    lightSources.forEach(lightSource => {
      const raySegments = rayTracer.generateRays(lightSource, lenses);
      totalRaySegments += raySegments.length;
    });
    
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    console.log(`✅ 性能测试完成:`);
    console.log(`  - 透镜数量: ${lenses.length}`);
    console.log(`  - 光源数量: ${lightSources.length}`);
    console.log(`  - 总光线段数: ${totalRaySegments}`);
    console.log(`  - 计算时间: ${duration.toFixed(2)}ms`);
    console.log(`  - 平均每条光线: ${(duration / totalRaySegments).toFixed(4)}ms`);
    
    // 清理资源
    lenses.forEach(lens => lens.dispose());
    lightSources.forEach(lightSource => lightSource.dispose());
  }
}
