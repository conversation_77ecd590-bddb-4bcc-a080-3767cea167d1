import * as THREE from 'three';

export class ModuleTest {
  public static async testModuleImports(): Promise<void> {
    console.log('🔍 测试模块导入...');
    
    try {
      // 测试Lens模块导入
      const { Lens, LensConfig } = await import('../optics/Lens');
      console.log('✓ Lens模块导入成功');
      console.log('✓ LensConfig接口导入成功');
      
      // 测试创建LensConfig对象
      const testConfig: LensConfig = {
        type: 'convex',
        position: new THREE.Vector3(0, 0, 0),
        focalLength: 5,
        diameter: 3,
        thickness: 0.5
      };
      console.log('✓ LensConfig对象创建成功');
      
      // 测试创建Lens实例
      const testLens = new Lens(testConfig);
      console.log('✓ Lens实例创建成功');
      testLens.dispose();
      
    } catch (error) {
      console.error('❌ Lens模块测试失败:', error);
    }
    
    try {
      // 测试LightSource模块导入
      const { PointLightSource, ParallelLightSource, LightSourceConfig } = await import('../optics/LightSource');
      console.log('✓ LightSource模块导入成功');
      
      // 测试创建LightSourceConfig对象
      const testLightConfig: LightSourceConfig = {
        position: new THREE.Vector3(-5, 0, 0),
        intensity: 1.0,
        color: 0xffffff,
        rayCount: 12
      };
      console.log('✓ LightSourceConfig对象创建成功');
      
      // 测试创建PointLightSource实例
      const testPointLight = new PointLightSource(testLightConfig);
      console.log('✓ PointLightSource实例创建成功');
      testPointLight.dispose();
      
    } catch (error) {
      console.error('❌ LightSource模块测试失败:', error);
    }
    
    try {
      // 测试管理器模块导入
      const { LensManager } = await import('../optics/LensManager');
      const { LightSourceManager } = await import('../optics/LightSourceManager');
      console.log('✓ 管理器模块导入成功');
      
      // 测试创建管理器实例
      const scene = new THREE.Scene();
      const lensManager = new LensManager(scene);
      const lightSourceManager = new LightSourceManager(scene);
      console.log('✓ 管理器实例创建成功');
      
      lensManager.dispose();
      lightSourceManager.dispose();
      
    } catch (error) {
      console.error('❌ 管理器模块测试失败:', error);
    }
    
    try {
      // 测试核心模块导入
      const { VirtualLab } = await import('../core/VirtualLab');
      console.log('✓ VirtualLab模块导入成功');
      
    } catch (error) {
      console.error('❌ VirtualLab模块测试失败:', error);
    }
    
    console.log('✅ 模块导入测试完成');
  }
  
  public static async testVirtualLabCreation(): Promise<boolean> {
    console.log('🧪 测试VirtualLab创建...');
    
    try {
      const { VirtualLab } = await import('../core/VirtualLab');
      
      console.log('正在创建VirtualLab实例...');
      const lab = new VirtualLab();
      console.log('✓ VirtualLab实例创建成功');
      
      console.log('正在初始化VirtualLab...');
      lab.init();
      console.log('✓ VirtualLab初始化成功');
      
      console.log('正在启动VirtualLab...');
      lab.start();
      console.log('✓ VirtualLab启动成功');
      
      console.log('✅ VirtualLab创建测试成功');
      return true;
      
    } catch (error) {
      console.error('❌ VirtualLab创建测试失败:', error);
      return false;
    }
  }
}
