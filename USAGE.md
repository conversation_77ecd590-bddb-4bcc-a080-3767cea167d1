# 虚拟光学实验室使用指南

## 🚀 快速开始

### 1. 启动项目
```bash
npm install
npm run dev
```

访问 http://localhost:5173 即可开始使用虚拟实验室。

### 2. 界面介绍

#### 主要区域
- **3D场景区域**：显示透镜、光源和光线的3D视图
- **控制面板**：右侧的参数控制界面
- **信息面板**：左上角的操作说明

#### 控制面板功能
- **透镜控制**：添加和调节透镜参数
- **光源控制**：添加和调节光源参数
- **演示场景**：预设的演示实验
- **渲染控制**：光线显示和渲染设置
- **场景控制**：网格和坐标轴显示
- **操作**：场景重置和相机控制

## 🔬 基础实验

### 实验1：凸透镜会聚光线

**目标**：观察平行光通过凸透镜后会聚到焦点的现象

**步骤**：
1. 点击"演示场景" → "凸透镜会聚"
2. 观察平行光线通过凸透镜后的路径
3. 注意光线会聚到焦点的位置

**观察要点**：
- 平行光线经过凸透镜后会聚
- 会聚点就是透镜的焦点
- 焦点距离透镜中心的距离等于焦距

### 实验2：凹透镜发散光线

**目标**：观察平行光通过凹透镜后发散的现象

**步骤**：
1. 点击"演示场景" → "凹透镜发散"
2. 观察平行光线通过凹透镜后的路径
3. 注意光线发散的规律

**观察要点**：
- 平行光线经过凹透镜后发散
- 发散光线的反向延长线会聚于虚焦点
- 虚焦点在透镜的入射光一侧

### 实验3：点光源成像

**目标**：观察点光源通过凸透镜的成像规律

**步骤**：
1. 点击"演示场景" → "点光源成像"
2. 观察从点光源发出的光线通过透镜后的路径
3. 尝试调节光源位置观察成像变化

**观察要点**：
- 点光源发出的光线经过透镜后会聚
- 会聚点的位置取决于光源到透镜的距离
- 符合薄透镜成像公式：1/f = 1/u + 1/v

## 🎛️ 自定义实验

### 添加透镜

1. **选择透镜类型**
   - 凸透镜（convex）：会聚光线
   - 凹透镜（concave）：发散光线

2. **设置透镜参数**
   - **焦距**：控制透镜的会聚/发散能力
   - **直径**：透镜的有效孔径
   - **厚度**：透镜的物理厚度

3. **设置透镜位置**
   - X、Y、Z坐标控制透镜在3D空间中的位置

4. **点击"添加透镜"**

### 添加光源

1. **选择光源类型**
   - **点光源**：从一点向各个方向发射光线
   - **平行光**：所有光线平行且方向相同

2. **设置光源参数**
   - **强度**：光源的亮度
   - **颜色**：光的颜色
   - **射线数量**：显示的光线条数

3. **设置光源位置**
   - X、Y、Z坐标控制光源位置

4. **设置平行光方向**（仅平行光）
   - 方向向量的X、Y、Z分量

5. **点击"添加光源"**

## 🎮 操作技巧

### 视角控制
- **旋转视角**：鼠标左键拖拽
- **缩放场景**：鼠标滚轮
- **平移视图**：鼠标右键拖拽

### 键盘快捷键
- **R键**：重置场景到默认状态
- **H键**：显示/隐藏控制面板

### 参数调节技巧
- **实时调节**：拖动滑块可实时看到效果
- **精确输入**：双击数值可直接输入精确值
- **批量操作**：使用演示场景快速设置复杂实验

## 🔍 高级功能

### 复杂光学系统

**双透镜系统**：
1. 点击"演示场景" → "双透镜系统"
2. 观察光线依次通过两个透镜的路径
3. 理解复合透镜系统的工作原理

**凹凸透镜组合**：
1. 点击"演示场景" → "复杂系统"
2. 观察凹透镜和凸透镜组合的效果
3. 理解光学系统的像差校正原理

### 光学现象观察

**焦距对比**：
1. 点击"演示场景" → "焦距对比"
2. 对比不同焦距透镜的会聚效果
3. 理解焦距对成像的影响

**彩色光源**：
1. 点击"演示场景" → "彩色光源"
2. 观察不同颜色光线的传播
3. 理解光的颜色特性

## 📊 实验数据分析

### 观察记录
在进行实验时，建议记录以下数据：
- 透镜类型和参数（焦距、直径）
- 光源类型和位置
- 光线会聚/发散的位置
- 成像的清晰度和大小

### 理论验证
可以使用以下公式验证实验结果：

**薄透镜公式**：
```
1/f = 1/u + 1/v
```
- f：焦距
- u：物距（光源到透镜距离）
- v：像距（透镜到成像点距离）

**放大率公式**：
```
M = v/u = h'/h
```
- M：放大率
- h'：像高
- h：物高

## 🛠️ 故障排除

### 常见问题

**Q: 看不到光线？**
A: 检查"渲染控制"中的"显示光线"是否开启

**Q: 光线显示不完整？**
A: 调整"最大光线长度"参数

**Q: 场景太暗？**
A: 增加光源强度或调整环境光设置

**Q: 操作不流畅？**
A: 减少射线数量或降低场景复杂度

### 性能优化
- 合理设置射线数量（建议8-20条）
- 避免同时添加过多透镜和光源
- 定期使用"清空场景"重置实验

## 📚 扩展学习

### 光学原理
- 几何光学基础
- 透镜成像理论
- 光的折射和反射
- 光学仪器原理

### 相关实验
- 显微镜成像原理
- 望远镜光学系统
- 照相机镜头设计
- 激光光学实验

通过这个虚拟实验室，您可以直观地理解光学原理，验证理论知识，并探索各种光学现象。祝您实验愉快！
